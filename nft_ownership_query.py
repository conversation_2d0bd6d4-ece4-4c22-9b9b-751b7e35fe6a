#!/usr/bin/env python3
"""
NFT持有者查询脚本
查询指定项目的NFT持有者统计，按持有数量排序显示
"""

import asyncio
import sys
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from ppp_api import PPPAPIClient


class NFTOwnershipQuery:
    """NFT持有者查询器"""
    
    def __init__(self):
        self.api_client = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.api_client = PPPAPIClient()
        await self.api_client.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.api_client:
            await self.api_client.__aexit__(exc_type, exc_val, exc_tb)
    
    async def get_project_list(self) -> List:
        """获取项目列表"""
        try:
            projects = await self.api_client.get_projects(limit=50)
            return projects
        except Exception as e:
            print(f"❌ 获取项目列表失败: {e}")
            return []
    
    async def get_nft_ownership_stats(self, project_mint: str) -> Tuple[Dict[str, List[int]], List[Tuple[int, int]]]:
        """获取NFT持有者统计

        Args:
            project_mint: 项目mint地址

        Returns:
            Dict[持有者地址, NFT_ID列表]
        """
        try:
            print(f"🔍 正在获取项目NFT数据...")
            print(f"项目mint地址: {project_mint}")

            # 获取项目所有NFT
            print("正在获取项目NFT...")
            nfts = await self.api_client.get_project_nfts(
                project_mint,
                limit=1000
            )

            if not nfts:
                print("❌ 该项目暂无NFT数据")
                print("可能原因：")
                print("1. 项目还未发行NFT")
                print("2. 项目mint地址不正确")
                print("3. API暂时无法访问该项目数据")
                return {}, []

            print(f"📊 获取到 {len(nfts)} 个NFT")

            # 统计持有者和交易时间
            ownership_stats = defaultdict(list)
            valid_nfts = 0
            trade_data = []  # 存储 (时间戳, NFT_ID) 元组

            for nft in nfts:
                nft_id = nft.id
                owner = nft.owner
                last_trade = nft.attributes.get("last_trade", 0)
                last_split = nft.attributes.get("last_split", 0)

                if nft_id is not None and owner:
                    ownership_stats[owner].append(nft_id)
                    valid_nfts += 1

                    # 收集交易时间和对应的NFT ID
                    # 排除0值和last_split与last_trade相同的情况（分割操作）
                    if (last_trade and last_trade > 0 and
                        last_trade != last_split):
                        trade_data.append((last_trade, nft_id))

            print(f"📈 有效NFT数量: {valid_nfts}")
            print(f"👥 持有者数量: {len(ownership_stats)}")
            print(f"📊 有效交易记录: {len(trade_data)} 个")

            # 返回持有者统计和交易数据
            return dict(ownership_stats), trade_data

        except Exception as e:
            print(f"❌ 获取NFT持有者数据失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return {}, []

    def display_trade_time_analysis(self, trade_data: List[Tuple[int, int]]):
        """显示交易时间分析"""
        print("\n" + "=" * 100)

        if len(trade_data) >= 2:
            # 找到最早和最晚的交易
            min_trade_data = min(trade_data, key=lambda x: x[0])
            max_trade_data = max(trade_data, key=lambda x: x[0])

            min_trade_time, min_nft_id = min_trade_data
            max_trade_time, max_nft_id = max_trade_data

            time_diff_seconds = max_trade_time - min_trade_time

            from datetime import datetime
            min_time_str = datetime.fromtimestamp(min_trade_time).strftime("%Y-%m-%d %H:%M:%S")
            max_time_str = datetime.fromtimestamp(max_trade_time).strftime("%Y-%m-%d %H:%M:%S")

            print(f"⏰ 交易时间分析:")
            print(f"   最早交易: {min_time_str} (NFT #{min_nft_id})")
            print(f"   最晚交易: {max_time_str} (NFT #{max_nft_id})")
            print(f"   时间跨度: {time_diff_seconds:.2f} 秒")
        elif len(trade_data) == 1:
            trade_time, nft_id = trade_data[0]
            from datetime import datetime
            time_str = datetime.fromtimestamp(trade_time).strftime("%Y-%m-%d %H:%M:%S")
            print(f"⏰ 仅有1个NFT有交易记录: {time_str} (NFT #{nft_id})")
        else:
            print(f"⏰ 暂无有效交易记录")

    def display_ownership_stats(self, ownership_stats: Dict[str, List[int]], project_name: str):
        """显示持有者统计信息
        
        Args:
            ownership_stats: 持有者统计数据
            project_name: 项目名称
        """
        if not ownership_stats:
            print("❌ 没有持有者数据")
            return
        
        print(f"\n📊 {project_name} NFT持有者统计")
        print("=" * 100)
        
        # 按持有数量排序
        sorted_owners = sorted(
            ownership_stats.items(), 
            key=lambda x: len(x[1]), 
            reverse=True
        )
        
        total_nfts = sum(len(nft_ids) for nft_ids in ownership_stats.values())
        total_owners = len(ownership_stats)
        
        print(f"总NFT数量: {total_nfts}")
        print(f"总持有者数量: {total_owners}")
        print(f"平均持有量: {total_nfts/total_owners:.2f}")
        print("=" * 100)
        
        # 表头
        print(f"{'排名':<6} {'持有者地址':<45} {'持有数量':<8} {'NFT ID (前10个)'}")
        print("-" * 100)
        
        # 显示持有者信息
        for rank, (owner, nft_ids) in enumerate(sorted_owners, 1):
            nft_count = len(nft_ids)
            
            # 显示前10个NFT ID
            nft_ids_sorted = sorted(nft_ids)
            nft_display = ", ".join(map(str, nft_ids_sorted[:10]))
            if len(nft_ids_sorted) > 10:
                nft_display += f" ... (+{len(nft_ids_sorted)-10}个)"
            
            print(f"{rank:<6} {owner:<45} {nft_count:<8} {nft_display}")
        
        print("=" * 100)
        
        # 持有量分布统计
        self.display_distribution_stats(ownership_stats)
    
    def display_distribution_stats(self, ownership_stats: Dict[str, List[int]]):
        """显示持有量分布统计"""
        holding_counts = [len(nft_ids) for nft_ids in ownership_stats.values()]
        count_distribution = Counter(holding_counts)
        
        print("\n📈 持有量分布:")
        print("-" * 50)
        
        # 按持有数量排序
        for count, num_owners in sorted(count_distribution.items()):
            percentage = (num_owners / len(ownership_stats)) * 100
            print(f"持有 {count:>3} 个NFT: {num_owners:>4} 人 ({percentage:>5.1f}%)")
        
        print("-" * 50)


def display_project_list(projects: List) -> Dict[int, Any]:
    """显示项目列表并返回选择映射"""
    print("\n📋 可用项目列表:")
    print("=" * 100)
    print(f"{'序号':<4} {'项目名称':<25} {'总供应量':<8} {'已发行':<8} {'地板价(SOL)':<12} {'Mint地址':<40}")
    print("-" * 100)

    project_map = {}
    for i, project in enumerate(projects, 1):
        name = project.name[:23] if len(project.name) > 23 else project.name
        mint = project.project_mint if project.project_mint else project.id
        total_supply = project.total_supply
        listed_count = project.listed_count
        floor_price = f"{project.floor_price:.4f}" if project.floor_price > 0 else "N/A"

        print(f"{i:<4} {name:<25} {total_supply:<8} {listed_count:<8} {floor_price:<12} {mint}")
        project_map[i] = project

    print("=" * 100)
    print("💡 建议选择已发行数量 > 0 的项目")
    return project_map


async def main():
    """主函数"""
    print("🔍 NFT持有者查询工具")
    print("=" * 60)
    
    try:
        async with NFTOwnershipQuery() as query:
            # 获取项目列表
            print("📋 正在获取项目列表...")
            projects = await query.get_project_list()
            
            if not projects:
                print("❌ 未找到任何项目")
                return
            
            # 显示项目列表
            project_map = display_project_list(projects)
            
            # 用户选择项目
            while True:
                try:
                    choice = input(f"\n请选择项目 (1-{len(projects)}, 0退出): ").strip()
                    
                    if choice == "0":
                        print("👋 退出查询")
                        return
                    
                    choice_num = int(choice)
                    if choice_num in project_map:
                        selected_project = project_map[choice_num]
                        break
                    else:
                        print("❌ 无效选择，请重新输入")
                        
                except ValueError:
                    print("❌ 请输入有效数字")
                except KeyboardInterrupt:
                    print("\n👋 用户中断，退出")
                    return
            
            # 查询选中项目的持有者统计
            project_name = selected_project.name
            project_mint = selected_project.project_mint if selected_project.project_mint else selected_project.id
            
            print(f"\n🔍 正在查询项目: {project_name}")
            print(f"Mint地址: {project_mint}")

            ownership_stats, trade_data = await query.get_nft_ownership_stats(project_mint)

            if ownership_stats:
                query.display_ownership_stats(ownership_stats, project_name)

                # 在最后显示交易时间分析
                query.display_trade_time_analysis(trade_data)
            else:
                print("❌ 未获取到持有者数据")
    
    except Exception as e:
        print(f"❌ 查询过程中出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
