# PPP.fun 简化买入器环境变量配置
# 复制此文件为 .env 并填入你的实际值

# ===== 必需配置 =====

# 钱包私钥 (Base58 格式)
WALLET_PRIVATE_KEY=your_base58_private_key_here

# ===== 可选配置 =====

# Solana RPC URL (默认使用主网)
RPC_URL=https://api.mainnet-beta.solana.com
# PRC_URL=https://rpc.ankr.com/solana/f6e797377dad5a7dafb093ccd943ae92218dbf03e4dc7fdf1eb5ffda9f6688b7
# 干运行模式 (true/false) - 设置为 true 时不会实际发送交易
DRY_RUN=true

# 最大重试次数 (默认为3)
MAX_RETRIES=3

# 超时时间，秒 (默认为30)
TIMEOUT=30

# ===== 使用说明 =====
# 1. 请确保私钥安全，不要提交到代码仓库
# 2. 首次使用建议保持 DRY_RUN=true 进行测试
# 3. 确认一切正常后再设置 DRY_RUN=false 进行真实交易
# 4. 程序会自动从 idl.json 和 api_result/nfts.json 读取必要信息
