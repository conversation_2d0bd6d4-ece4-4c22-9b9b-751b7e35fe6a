#!/usr/bin/env python3
"""
PPP.fun 自动交易启动脚本
提供配置选择和便捷启动功能
"""

import asyncio
import sys
import os
import subprocess
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from auto_trading.models import AutoBuyConfig


def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🚀 PPP.fun 自动交易启动器")
    print("=" * 60)
    print("选择配置文件并启动自动交易")
    print("=" * 60)


def scan_config_files():
    """扫描可用的配置文件"""
    config_dir = Path("auto_buy_configs")
    
    if not config_dir.exists():
        print("❌ 配置目录不存在，请先创建配置文件")
        return []
    
    config_files = list(config_dir.glob("*.json"))
    
    if not config_files:
        print("❌ 未找到配置文件，请先创建配置")
        return []
    
    return sorted(config_files)


def display_config_list(config_files):
    """显示配置文件列表"""
    print("\n📋 可用配置文件:")
    print("=" * 80)
    print(f"{'序号':<4} {'配置名称':<30} {'文件名':<25} {'模式':<10}")
    print("-" * 80)
    
    for i, config_file in enumerate(config_files, 1):
        try:
            # 读取配置文件获取详细信息
            config = AutoBuyConfig.from_file(str(config_file))
            config_name = config.name[:28] + ".." if len(config.name) > 30 else config.name
            file_name = config_file.name[:23] + ".." if len(config_file.name) > 25 else config_file.name
            mode = "干运行" if config.dry_run else "真实交易"
            
            print(f"{i:<4} {config_name:<30} {file_name:<25} {mode:<10}")
            
        except Exception as e:
            file_name = config_file.name[:23] + ".." if len(config_file.name) > 25 else config_file.name
            print(f"{i:<4} {'配置读取失败':<30} {file_name:<25} {'未知':<10}")
    
    print("=" * 80)



def check_dry_run_mode(config_file):
    """检查配置的干运行模式"""
    try:
        config = AutoBuyConfig.from_file(str(config_file))
        return config.dry_run
    except Exception as e:
        print(f"⚠️ 读取配置失败: {e}")
        return True  # 默认为干运行模式


def start_auto_trading(config_file_path):
    """启动自动交易

    Returns:
        bool: True表示正常完成，False表示用户中断
    """
    try:
        # 构建命令
        cmd = [
            sys.executable,
            "auto_trading_cli.py",
            "start",
            str(config_file_path)
        ]

        print(f"🚀 启动命令: {' '.join(cmd)}")
        print("=" * 60)

        # 执行命令
        result = subprocess.run(cmd, cwd=Path(__file__).parent)

        if result.returncode == 0:
            print("\n✅ 自动交易完成")
        elif result.returncode == 130:  # SIGINT (Ctrl+C)
            print("\n🛑 用户中断操作")
            return False  # 用户中断
        else:
            print(f"\n❌ 自动交易异常退出，返回码: {result.returncode}")

        return True  # 正常完成

    except KeyboardInterrupt:
        print("\n🛑 用户中断操作")
        return False  # 用户中断
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return True  # 错误不算中断


def create_new_config():
    """创建新配置"""
    try:
        cmd = [sys.executable, "auto_trading_cli.py", "create-config"]
        print(f"📝 启动配置生成器...")
        print("=" * 60)
        
        result = subprocess.run(cmd, cwd=Path(__file__).parent)
        
        if result.returncode == 0:
            print("\n✅ 配置创建完成")
            return True
        else:
            print(f"\n❌ 配置创建失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 启动配置生成器失败: {e}")
        return False


def main():
    """主函数"""
    print_banner()
    
    while True:
        try:
            # 扫描配置文件
            config_files = scan_config_files()
            
            if not config_files:
                print("\n💡 选项:")
                print("1. 创建新配置")
                print("0. 退出")
                
                choice = input("\n请选择 (1/0): ").strip()
                
                if choice == "1":
                    if create_new_config():
                        continue  # 重新扫描配置文件
                    else:
                        break
                elif choice == "0":
                    print("👋 退出程序")
                    break
                else:
                    print("❌ 无效选择")
                    continue
            
            # 显示配置列表
            display_config_list(config_files)

            print("\n💡 选项:")
            print("c. 创建新配置")
            print("0. 退出")

            # 获取用户选择
            choice = input(f"\n请选择配置 (1-{len(config_files)}, c=创建配置, 0=退出): ").strip().lower()

            if choice == "0":
                print("👋 退出程序")
                break
            elif choice == "c":
                create_new_config()
                continue

            # 选择配置文件
            try:
                index = int(choice) - 1
                if 0 <= index < len(config_files):
                    selected_config = config_files[index]
                    print(f"✅ 已选择配置: {selected_config.name}")
                else:
                    print(f"❌ 无效选择，请输入 1-{len(config_files)} 之间的数字")
                    continue
            except ValueError:
                print("❌ 请输入有效的数字或字母")
                continue
            
            # 启动自动交易
            trading_completed = start_auto_trading(selected_config)

            # 如果用户中断，直接退出
            if not trading_completed:
                break

            # 询问是否继续
            continue_choice = input("\n是否继续选择其他配置? (Y/n): ").strip().lower()
            if continue_choice in ['n', 'no']:
                break
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 程序出错: {e}")
            break


if __name__ == "__main__":
    main()
