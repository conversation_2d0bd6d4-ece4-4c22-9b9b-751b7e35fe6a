"""
Bark 推送通知模块
支持完整的 Bark API 功能，包括基础推送、高级功能和加密推送
集成到现有的日志系统中
"""

import os
import json
import time
import hashlib
import base64
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import Optional, Dict, Any, Union, List
from enum import Enum
from dataclasses import dataclass, asdict
from urllib.parse import quote

import requests
from loguru import logger
from log_config import setup_module_logging, get_logger

# 自动加载 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # 如果没有安装 python-dotenv，忽略错误
    pass


class NotificationLevel(Enum):
    """通知级别枚举"""
    ACTIVE = "active"  # 默认值，立即显示通知并点亮屏幕
    TIME_SENSITIVE = "timeSensitive"  # 时效性通知，可在专注模式下显示
    PASSIVE = "passive"  # 被动通知，添加到通知列表但不点亮屏幕
    CRITICAL = "critical"  # 关键警报，忽略静音和勿扰模式


class SoundType(Enum):
    """声音类型枚举"""
    DEFAULT = "default"
    ALARM = "alarm"
    ANTICIPATE = "anticipate"
    BELL = "bell"
    BIRDSONG = "birdsong"
    BLOOM = "bloom"
    CALYPSO = "calypso"
    CHIME = "chime"
    CHOO = "choo"
    DESCENT = "descent"
    ELECTRONIC = "electronic"
    FANFARE = "fanfare"
    GLASS = "glass"
    GOTOSLEEP = "gotosleep"
    HEALTHNOTIFICATION = "healthnotification"
    HORN = "horn"
    LADDER = "ladder"
    MAILSENT = "mailsent"
    MINUET = "minuet"
    MULTIWAYINVITATION = "multiwayinvitation"
    NEWMAIL = "newmail"
    NEWSFLASH = "newsflash"
    NOIR = "noir"
    PAYMENTSUCCESS = "paymentsuccess"
    SHAKE = "shake"
    SHERWOODFOREST = "sherwoodforest"
    SILENCE = "silence"
    SPELL = "spell"
    SUSPENSE = "suspense"
    TELEGRAPH = "telegraph"
    TIPTOES = "tiptoes"
    TYPEWRITERS = "typewriters"
    UPDATE = "update"


@dataclass
class BarkMessage:
    """Bark 消息数据类"""
    title: str
    body: str
    subtitle: Optional[str] = None
    url: Optional[str] = None
    group: Optional[str] = None
    icon: Optional[str] = None
    sound: Optional[Union[str, SoundType]] = None
    level: Optional[Union[str, NotificationLevel]] = None
    call: Optional[bool] = None
    ciphertext: Optional[str] = None
    badge: Optional[int] = None
    automatically_copy: Optional[str] = None
    copy: Optional[str] = None


class BarkNotifier:
    """Bark 推送通知器"""

    def __init__(
        self,
        key: Optional[str] = None,
        server_url: str = "https://api.day.app",
        timeout: int = 30,
        max_retries: int = 3,
        enable_logging: bool = True,
        log_level: str = "INFO",
        async_mode: bool = True,
        max_workers: int = 3
    ):
        """
        初始化 Bark 通知器

        Args:
            key: Bark 推送密钥，如果为空则从环境变量 BARK_KEY 读取
            server_url: Bark 服务器地址，默认使用官方服务器
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            enable_logging: 是否启用日志
            log_level: 日志级别
            async_mode: 是否启用异步模式（推送不阻塞主线程）
            max_workers: 异步模式下的最大工作线程数
        """
        self.key = key or os.getenv("BARK_KEY")
        if not self.key:
            raise ValueError("Bark key 未设置，请设置环境变量 BARK_KEY 或传入 key 参数")

        self.server_url = server_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        self.async_mode = async_mode
        self.max_workers = max_workers

        # 初始化线程池（用于异步推送）
        if self.async_mode:
            self.executor = ThreadPoolExecutor(max_workers=self.max_workers, thread_name_prefix="bark_")
        else:
            self.executor = None

        # 设置日志
        if enable_logging:
            setup_module_logging("bark_notifier", log_level)
        self.logger = get_logger()

        self.logger.info(f"🔔 Bark 通知器初始化完成")
        self.logger.debug(f"   服务器: {self.server_url}")
        self.logger.debug(f"   密钥: {self.key[:8]}...")
        self.logger.debug(f"   异步模式: {'启用' if self.async_mode else '禁用'}")
        if self.async_mode:
            self.logger.debug(f"   工作线程数: {self.max_workers}")
        
    def _build_url(self, message: BarkMessage) -> str:
        """构建推送 URL"""
        # 基础 URL 结构: /:key/:title/:subtitle/:body
        url_parts = [self.server_url, self.key]
        
        if message.title:
            url_parts.append(quote(message.title))
        if message.subtitle:
            url_parts.append(quote(message.subtitle))
        if message.body:
            url_parts.append(quote(message.body))
            
        return "/".join(url_parts)
    
    def _build_params(self, message: BarkMessage) -> Dict[str, Any]:
        """构建请求参数"""
        params = {}
        
        # 处理各种参数
        if message.url:
            params["url"] = message.url
        if message.group:
            params["group"] = message.group
        if message.icon:
            params["icon"] = message.icon
        if message.sound:
            if isinstance(message.sound, SoundType):
                params["sound"] = message.sound.value
            else:
                params["sound"] = message.sound
        if message.level:
            if isinstance(message.level, NotificationLevel):
                params["level"] = message.level.value
            else:
                params["level"] = message.level
        if message.call is not None:
            params["call"] = "1" if message.call else "0"
        if message.ciphertext:
            params["ciphertext"] = message.ciphertext
        if message.badge is not None:
            params["badge"] = str(message.badge)
        if message.automatically_copy:
            params["automaticallyCopy"] = message.automatically_copy
        if message.copy:
            params["copy"] = message.copy
            
        return params
    
    def _send_request(self, url: str, params: Dict[str, Any], method: str = "GET") -> Dict[str, Any]:
        """发送 HTTP 请求"""
        for attempt in range(self.max_retries + 1):
            try:
                if method.upper() == "GET":
                    response = requests.get(url, params=params, timeout=self.timeout)
                else:
                    # POST 请求，将参数放在请求体中
                    response = requests.post(url, json=params, timeout=self.timeout)

                response.raise_for_status()

                # 尝试解析 JSON 响应
                try:
                    result = response.json()
                except json.JSONDecodeError:
                    result = {"message": response.text, "code": response.status_code}

                self.logger.debug(f"📤 推送响应: {result}")
                return result

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"⚠️ 推送请求失败 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}")
                if attempt == self.max_retries:
                    # 最后一次尝试失败，返回错误结果而不是抛出异常
                    return {"error": str(e), "code": -1}
                time.sleep(2 ** attempt)  # 指数退避

        # 理论上不会到达这里，但为了类型安全
        return {"error": "未知错误", "code": -1}

    def _safe_send_request(self, url: str, params: Dict[str, Any], method: str = "GET") -> Dict[str, Any]:
        """
        安全发送请求，捕获所有异常，确保不影响主业务

        Args:
            url: 请求 URL
            params: 请求参数
            method: HTTP 方法

        Returns:
            推送结果字典，失败时返回错误信息
        """
        try:
            return self._send_request(url, params, method)
        except Exception as e:
            # 捕获所有异常，记录日志但不抛出
            self.logger.warning(f"⚠️ Bark 推送失败（已忽略）: {e}")
            return {"error": str(e), "code": -1, "ignored": True}

    def _async_send_wrapper(self, message: BarkMessage, method: str = "GET") -> None:
        """
        异步发送包装器，用于在后台线程中执行推送

        Args:
            message: Bark 消息对象
            method: HTTP 方法
        """
        try:
            url = self._build_url(message)
            params = self._build_params(message)

            self.logger.debug(f"📤 异步发送 Bark 推送: {message.title}")
            result = self._safe_send_request(url, params, method)

            # 检查推送结果
            if result.get("code") == 200 or result.get("message") == "success":
                self.logger.debug(f"✅ Bark 异步推送成功: {message.title}")
            elif result.get("ignored"):
                self.logger.debug(f"⚠️ Bark 异步推送被忽略: {message.title}")
            else:
                self.logger.debug(f"⚠️ Bark 异步推送可能失败: {message.title} - {result}")

        except Exception as e:
            # 最后的安全网，确保异步线程中的异常不会影响主程序
            self.logger.warning(f"⚠️ Bark 异步推送异常（已忽略）: {e}")

    def __del__(self):
        """析构函数，清理线程池"""
        if hasattr(self, 'executor') and self.executor:
            try:
                self.executor.shutdown(wait=False)
            except Exception:
                pass  # 忽略清理时的异常
    
    def send(
        self,
        title: str,
        body: str,
        subtitle: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送基础推送消息
        
        Args:
            title: 推送标题
            body: 推送内容
            subtitle: 推送副标题
            **kwargs: 其他参数，参见 BarkMessage
            
        Returns:
            推送结果字典
        """
        message = BarkMessage(
            title=title,
            body=body,
            subtitle=subtitle,
            **kwargs
        )
        return self.send_message(message)
    
    def send_message(
        self,
        message: BarkMessage,
        method: str = "GET",
        async_send: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        发送 Bark 消息

        Args:
            message: Bark 消息对象
            method: HTTP 方法，GET 或 POST
            async_send: 是否异步发送，None 时使用实例默认设置

        Returns:
            推送结果字典（异步模式下返回占位结果）
        """
        # 确定是否使用异步模式
        use_async = async_send if async_send is not None else self.async_mode

        if use_async and self.executor:
            # 异步模式：提交到线程池，不阻塞主线程
            self.logger.debug(f"📤 异步提交 Bark 推送: {message.title}")

            try:
                # 提交异步任务
                self.executor.submit(self._async_send_wrapper, message, method)

                # 返回占位结果，表示任务已提交
                return {
                    "async": True,
                    "submitted": True,
                    "message": "推送任务已提交到后台线程",
                    "title": message.title
                }

            except Exception as e:
                # 如果异步提交失败，记录警告但不影响主业务
                self.logger.warning(f"⚠️ Bark 异步推送提交失败（已忽略）: {e}")
                return {
                    "async": True,
                    "submitted": False,
                    "error": str(e),
                    "ignored": True
                }
        else:
            # 同步模式：直接发送（使用安全包装器）
            try:
                url = self._build_url(message)
                params = self._build_params(message)

                self.logger.info(f"📤 同步发送 Bark 推送: {message.title}")
                self.logger.debug(f"   URL: {url}")
                self.logger.debug(f"   参数: {params}")

                result = self._safe_send_request(url, params, method)

                # 检查推送结果
                if result.get("code") == 200 or result.get("message") == "success":
                    self.logger.info(f"✅ Bark 推送发送成功")
                elif result.get("ignored"):
                    self.logger.info(f"⚠️ Bark 推送失败但已忽略")
                else:
                    self.logger.warning(f"⚠️ Bark 推送可能失败: {result}")

                return result

            except Exception as e:
                # 最后的安全网：记录错误但不抛出异常
                self.logger.warning(f"⚠️ Bark 推送发送失败（已忽略）: {e}")
                return {"error": str(e), "code": -1, "ignored": True}

    def send_with_url(
        self,
        title: str,
        body: str,
        url: str,
        subtitle: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送带跳转链接的推送

        Args:
            title: 推送标题
            body: 推送内容
            url: 点击推送后跳转的 URL
            subtitle: 推送副标题
            **kwargs: 其他参数

        Returns:
            推送结果字典
        """
        return self.send(title=title, body=body, subtitle=subtitle, url=url, **kwargs)

    def send_grouped(
        self,
        title: str,
        body: str,
        group: str,
        subtitle: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送分组推送

        Args:
            title: 推送标题
            body: 推送内容
            group: 分组名称
            subtitle: 推送副标题
            **kwargs: 其他参数

        Returns:
            推送结果字典
        """
        return self.send(title=title, body=body, subtitle=subtitle, group=group, **kwargs)

    def send_with_sound(
        self,
        title: str,
        body: str,
        sound: Union[str, SoundType],
        subtitle: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送带自定义声音的推送

        Args:
            title: 推送标题
            body: 推送内容
            sound: 声音类型
            subtitle: 推送副标题
            **kwargs: 其他参数

        Returns:
            推送结果字典
        """
        return self.send(title=title, body=body, subtitle=subtitle, sound=sound, **kwargs)

    def send_call(
        self,
        title: str,
        body: str,
        subtitle: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送持续响铃推送（30秒）

        Args:
            title: 推送标题
            body: 推送内容
            subtitle: 推送副标题
            **kwargs: 其他参数

        Returns:
            推送结果字典
        """
        return self.send(title=title, body=body, subtitle=subtitle, call=True, **kwargs)

    def send_time_sensitive(
        self,
        title: str,
        body: str,
        subtitle: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送时效性通知（可在专注模式下显示）

        Args:
            title: 推送标题
            body: 推送内容
            subtitle: 推送副标题
            **kwargs: 其他参数

        Returns:
            推送结果字典
        """
        return self.send(
            title=title,
            body=body,
            subtitle=subtitle,
            level=NotificationLevel.TIME_SENSITIVE,
            **kwargs
        )

    def send_critical(
        self,
        title: str,
        body: str,
        subtitle: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送关键警报（忽略静音和勿扰模式）

        Args:
            title: 推送标题
            body: 推送内容
            subtitle: 推送副标题
            **kwargs: 其他参数

        Returns:
            推送结果字典
        """
        return self.send(
            title=title,
            body=body,
            subtitle=subtitle,
            level=NotificationLevel.CRITICAL,
            **kwargs
        )

    def send_with_icon(
        self,
        title: str,
        body: str,
        icon_url: str,
        subtitle: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送带自定义图标的推送（iOS 15+）

        Args:
            title: 推送标题
            body: 推送内容
            icon_url: 图标 URL
            subtitle: 推送副标题
            **kwargs: 其他参数

        Returns:
            推送结果字典
        """
        return self.send(title=title, body=body, subtitle=subtitle, icon=icon_url, **kwargs)

    def send_encrypted(
        self,
        title: str,
        body: str,
        encryption_key: str,
        subtitle: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送加密推送

        Args:
            title: 推送标题
            body: 推送内容
            encryption_key: 加密密钥
            subtitle: 推送副标题
            **kwargs: 其他参数

        Returns:
            推送结果字典
        """
        # 简单的加密实现（实际应用中应使用更安全的加密方法）
        message_text = f"{title}\n{subtitle or ''}\n{body}".strip()
        encrypted_text = self._encrypt_message(message_text, encryption_key)

        return self.send(
            title="加密消息",
            body="请在 Bark 应用中查看",
            ciphertext=encrypted_text,
            **kwargs
        )

    def _encrypt_message(self, message: str, key: str) -> str:
        """
        简单的消息加密（示例实现）
        实际应用中应使用更安全的加密算法
        """
        # 这里使用简单的 base64 编码作为示例
        # 实际应用中应使用 AES 等加密算法
        combined = f"{key}:{message}"
        encoded = base64.b64encode(combined.encode('utf-8')).decode('utf-8')
        return encoded

    def send_batch(self, messages: List[BarkMessage], delay: float = 0.5) -> List[Dict[str, Any]]:
        """
        批量发送推送消息

        Args:
            messages: 消息列表
            delay: 消息间延迟（秒）

        Returns:
            推送结果列表
        """
        results = []
        self.logger.info(f"📤 开始批量发送 {len(messages)} 条 Bark 推送")

        for i, message in enumerate(messages, 1):
            try:
                result = self.send_message(message)
                results.append(result)
                self.logger.debug(f"   批量推送 {i}/{len(messages)} 完成")

                # 添加延迟避免频率限制
                if i < len(messages) and delay > 0:
                    time.sleep(delay)

            except Exception as e:
                self.logger.error(f"❌ 批量推送 {i}/{len(messages)} 失败: {e}")
                results.append({"error": str(e), "message_index": i})

        self.logger.info(f"✅ 批量推送完成，成功 {len([r for r in results if 'error' not in r])}/{len(messages)} 条")
        return results

    def send_template(
        self,
        template_name: str,
        variables: Dict[str, Any],
        templates: Optional[Dict[str, BarkMessage]] = None
    ) -> Dict[str, Any]:
        """
        使用模板发送推送

        Args:
            template_name: 模板名称
            variables: 模板变量
            templates: 自定义模板字典，如果为空则使用内置模板

        Returns:
            推送结果字典
        """
        if templates is None:
            templates = self._get_builtin_templates()

        if template_name not in templates:
            raise ValueError(f"模板 '{template_name}' 不存在")

        template = templates[template_name]

        # 替换模板变量
        message = BarkMessage(
            title=self._replace_variables(template.title, variables),
            body=self._replace_variables(template.body, variables),
            subtitle=self._replace_variables(template.subtitle, variables) if template.subtitle else None,
            url=template.url,
            group=template.group,
            icon=template.icon,
            sound=template.sound,
            level=template.level,
            call=template.call,
            badge=template.badge
        )

        return self.send_message(message)

    def _replace_variables(self, text: str, variables: Dict[str, Any]) -> str:
        """替换模板变量"""
        if not text:
            return text

        for key, value in variables.items():
            text = text.replace(f"{{{key}}}", str(value))

        return text

    def _get_builtin_templates(self) -> Dict[str, BarkMessage]:
        """获取内置模板"""
        return {
            "success": BarkMessage(
                title="✅ 操作成功",
                body="{message}",
                sound=SoundType.PAYMENTSUCCESS,
                level=NotificationLevel.ACTIVE
            ),
            "error": BarkMessage(
                title="❌ 操作失败",
                body="{message}",
                sound=SoundType.ALARM,
                level=NotificationLevel.CRITICAL
            ),
            "warning": BarkMessage(
                title="⚠️ 警告",
                body="{message}",
                sound=SoundType.ANTICIPATE,
                level=NotificationLevel.TIME_SENSITIVE
            ),
            "info": BarkMessage(
                title="ℹ️ 信息",
                body="{message}",
                sound=SoundType.DEFAULT,
                level=NotificationLevel.ACTIVE
            ),
            "trading_success": BarkMessage(
                title="🎉 交易成功",
                body="项目: {project}\nNFT ID: {nft_id}\n价格: {price} SOL",
                group="trading",
                sound=SoundType.PAYMENTSUCCESS,
                level=NotificationLevel.TIME_SENSITIVE
            ),
            "trading_error": BarkMessage(
                title="💥 交易失败",
                body="项目: {project}\n错误: {error}",
                group="trading",
                sound=SoundType.ALARM,
                level=NotificationLevel.CRITICAL
            ),
            "system_alert": BarkMessage(
                title="🚨 系统警报",
                body="{message}",
                sound=SoundType.ALARM,
                level=NotificationLevel.CRITICAL,
                call=True
            )
        }


class BarkIntegration:
    """Bark 集成助手类，提供与项目其他模块的集成功能"""

    def __init__(self, notifier: BarkNotifier):
        """
        初始化集成助手

        Args:
            notifier: Bark 通知器实例
        """
        self.notifier = notifier
        self.logger = get_logger()

    def notify_trading_result(
        self,
        success: bool,
        project_name: str,
        nft_id: Optional[str] = None,
        price: Optional[float] = None,
        error_message: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送交易结果通知

        Args:
            success: 交易是否成功
            project_name: 项目名称
            nft_id: NFT ID
            price: 交易价格
            error_message: 错误信息（失败时）
            **kwargs: 其他参数

        Returns:
            推送结果字典
        """
        if success:
            return self.notifier.send_template(
                "trading_success",
                {
                    "project": project_name,
                    "nft_id": nft_id or "未知",
                    "price": price or "未知"
                },
                **kwargs
            )
        else:
            return self.notifier.send_template(
                "trading_error",
                {
                    "project": project_name,
                    "error": error_message or "未知错误"
                },
                **kwargs
            )

    def notify_system_status(
        self,
        status: str,
        message: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发送系统状态通知

        Args:
            status: 状态类型 (success, error, warning, info)
            message: 状态消息
            **kwargs: 其他参数

        Returns:
            推送结果字典
        """
        template_map = {
            "success": "success",
            "error": "error",
            "warning": "warning",
            "info": "info"
        }

        template_name = template_map.get(status, "info")
        return self.notifier.send_template(template_name, {"message": message}, **kwargs)

    def notify_log_error(self, error_message: str, module_name: str = "", **kwargs) -> Dict[str, Any]:
        """
        发送日志错误通知

        Args:
            error_message: 错误消息
            module_name: 模块名称
            **kwargs: 其他参数

        Returns:
            推送结果字典
        """
        title = f"🔥 {module_name} 错误" if module_name else "🔥 系统错误"
        return self.notifier.send_critical(
            title=title,
            body=error_message,
            group="system_errors",
            **kwargs
        )


# 全局 Bark 通知器实例
_global_notifier: Optional[BarkNotifier] = None
_global_integration: Optional[BarkIntegration] = None


def init_bark(
    key: Optional[str] = None,
    server_url: str = "https://api.day.app",
    **kwargs
) -> BarkNotifier:
    """
    初始化全局 Bark 通知器

    Args:
        key: Bark 推送密钥
        server_url: Bark 服务器地址
        **kwargs: 其他初始化参数

    Returns:
        Bark 通知器实例
    """
    global _global_notifier, _global_integration

    _global_notifier = BarkNotifier(key=key, server_url=server_url, **kwargs)
    _global_integration = BarkIntegration(_global_notifier)

    return _global_notifier


def get_notifier() -> BarkNotifier:
    """获取全局 Bark 通知器实例"""
    if _global_notifier is None:
        raise RuntimeError("Bark 通知器未初始化，请先调用 init_bark()")
    return _global_notifier


def get_integration() -> BarkIntegration:
    """获取全局 Bark 集成助手实例"""
    if _global_integration is None:
        raise RuntimeError("Bark 集成助手未初始化，请先调用 init_bark()")
    return _global_integration


# 便捷函数（安全版本，不会抛出异常）
def bark_send(title: str, body: str, **kwargs) -> Dict[str, Any]:
    """便捷发送函数（安全版本）"""
    try:
        return get_notifier().send(title, body, **kwargs)
    except Exception as e:
        logger.warning(f"⚠️ bark_send 失败（已忽略）: {e}")
        return {"error": str(e), "ignored": True}


def bark_success(message: str, **kwargs) -> Dict[str, Any]:
    """发送成功通知（安全版本）"""
    try:
        return get_integration().notify_system_status("success", message, **kwargs)
    except Exception as e:
        logger.warning(f"⚠️ bark_success 失败（已忽略）: {e}")
        return {"error": str(e), "ignored": True}


def bark_error(message: str, **kwargs) -> Dict[str, Any]:
    """发送错误通知（安全版本）"""
    try:
        return get_integration().notify_system_status("error", message, **kwargs)
    except Exception as e:
        logger.warning(f"⚠️ bark_error 失败（已忽略）: {e}")
        return {"error": str(e), "ignored": True}


def bark_warning(message: str, **kwargs) -> Dict[str, Any]:
    """发送警告通知（安全版本）"""
    try:
        return get_integration().notify_system_status("warning", message, **kwargs)
    except Exception as e:
        logger.warning(f"⚠️ bark_warning 失败（已忽略）: {e}")
        return {"error": str(e), "ignored": True}


def bark_info(message: str, **kwargs) -> Dict[str, Any]:
    """发送信息通知（安全版本）"""
    try:
        return get_integration().notify_system_status("info", message, **kwargs)
    except Exception as e:
        logger.warning(f"⚠️ bark_info 失败（已忽略）: {e}")
        return {"error": str(e), "ignored": True}


def bark_trading_result(
    success: bool,
    project_name: str,
    nft_id: Optional[str] = None,
    price: Optional[float] = None,
    error_message: Optional[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """发送交易结果通知（安全版本）"""
    try:
        return get_integration().notify_trading_result(
            success=success,
            project_name=project_name,
            nft_id=nft_id,
            price=price,
            error_message=error_message,
            **kwargs
        )
    except Exception as e:
        logger.warning(f"⚠️ bark_trading_result 失败（已忽略）: {e}")
        return {"error": str(e), "ignored": True}


# 额外的安全函数
def safe_bark_send(title: str, body: str, **kwargs) -> bool:
    """
    安全发送 Bark 推送，只返回成功/失败状态

    Args:
        title: 推送标题
        body: 推送内容
        **kwargs: 其他参数

    Returns:
        bool: 是否成功发送
    """
    try:
        result = bark_send(title, body, **kwargs)
        return not result.get("error") and not result.get("ignored")
    except Exception:
        return False


def safe_bark_notify(level: str, message: str, **kwargs) -> bool:
    """
    安全发送通知，根据级别自动选择函数

    Args:
        level: 通知级别 (success, error, warning, info)
        message: 通知消息
        **kwargs: 其他参数

    Returns:
        bool: 是否成功发送
    """
    try:
        if level == "success":
            result = bark_success(message, **kwargs)
        elif level == "error":
            result = bark_error(message, **kwargs)
        elif level == "warning":
            result = bark_warning(message, **kwargs)
        else:
            result = bark_info(message, **kwargs)

        return not result.get("error") and not result.get("ignored")
    except Exception:
        return False


def safe_bark_notify_with_title(title: str, body: str, notify_type: str = "info", **kwargs) -> bool:
    """
    通用的安全 Bark 推送函数，支持自定义标题和内容

    Args:
        title: 推送标题
        body: 推送内容
        notify_type: 通知类型 (info, success, warning, error)
        **kwargs: 其他参数

    Returns:
        bool: 是否成功发送
    """
    try:
        # 确保 Bark 已初始化
        try:
            get_notifier()
        except RuntimeError:
            # 如果未初始化，尝试初始化
            try:
                init_bark()
            except Exception:
                return False

        if notify_type == "success":
            result = bark_success(f"{title}: {body}", **kwargs)
        elif notify_type == "warning":
            result = bark_warning(f"{title}: {body}", **kwargs)
        elif notify_type == "error":
            result = bark_error(f"{title}: {body}", **kwargs)
        else:
            result = bark_send(title, body, **kwargs)

        return not result.get("error") and not result.get("ignored")
    except Exception:
        return False


if __name__ == "__main__":
    """使用示例和测试"""
    import sys

    # 检查是否设置了 BARK_KEY 环境变量
    if not os.getenv("BARK_KEY"):
        print("❌ 请设置环境变量 BARK_KEY")
        print("   export BARK_KEY=your_bark_key")
        sys.exit(1)

    # 初始化 Bark 通知器
    try:
        notifier = init_bark()
        print("✅ Bark 通知器初始化成功")

        # 测试基础推送
        print("\n📤 测试基础推送...")
        result = bark_send("测试标题", "这是一条测试消息")
        print(f"结果: {result}")

        # 测试模板推送
        print("\n📤 测试模板推送...")
        result = bark_success("模块测试成功")
        print(f"结果: {result}")

        # 测试交易通知
        print("\n📤 测试交易通知...")
        result = bark_trading_result(
            success=True,
            project_name="测试项目",
            nft_id="123",
            price=0.1
        )
        print(f"结果: {result}")

        print("\n✅ 所有测试完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)
