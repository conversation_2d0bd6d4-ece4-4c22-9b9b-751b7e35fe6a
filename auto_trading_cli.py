#!/usr/bin/env python3
"""
PPP.fun 自动交易命令行工具
独立的自动交易功能入口，提供配置生成和交易启动功能
"""

import asyncio
import sys
import argparse
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 日志配置
from log_config import setup_auto_trading_logging, get_logger, set_project_name

# 日志配置将在获取配置文件路径后初始化
logger = None

# 设置第三方库的日志级别
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)

# 延迟导入，避免在设置项目名称之前初始化日志


def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🤖 PPP.fun 自动交易工具")
    print("=" * 60)
    print("独立的自动交易命令行工具")
    print("支持配置生成和自动交易启动")
    print("=" * 60)


async def create_config_command():
    """创建配置命令"""
    try:
        # 导入所需模块
        from ppp_api import PPPAPIClient
        from auto_trading.config_generator import ConfigGenerator

        print("📝 启动配置生成器...")

        # 创建API客户端
        async with PPPAPIClient() as api_client:
            generator = ConfigGenerator(api_client)
            
            # 交互式创建配置
            config = await generator.interactive_config_creation()
            
            if config is None:
                print("❌ 配置创建已取消")
                return
            
            # 显示配置摘要
            generator.display_config_summary(config)
            
            # 确认保存
            save_confirm = input("\n是否保存配置? (Y/n): ").strip().lower()
            if save_confirm in ['n', 'no']:
                print("❌ 配置未保存")
                return
            
            # 保存配置
            file_path = generator.save_config_to_file(config)
            print(f"✅ 配置已保存到: {file_path}")
            
            # 提供使用提示
            print(f"\n💡 使用提示:")
            print(f"   启动自动交易: python auto_trading_cli.py start {file_path}")
            print(f"   或使用原工具: python run_simple_buyer.py {file_path} --action auto-trade")
            
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
    except Exception as e:
        print(f"❌ 配置生成失败: {e}")
        import traceback
        traceback.print_exc()


async def start_trading_command(config_file_path: str):
    """启动自动交易命令

    Args:
        config_file_path: 配置文件路径
    """
    try:
        # 导入所需模块
        from simple_config import load_config
        from ppp_api import PPPAPIClient, ProjectDataManager
        from ppp_simple_buyer import PPPSimpleBuyer
        from auto_trading.models import AutoBuyConfig
        from auto_trading.scheduler import TradingScheduler
        from auto_trading.trader import ConcurrentTrader

        # 初始化日志配置（基于配置文件路径）
        setup_auto_trading_logging(config_file_path)
        global logger
        logger = get_logger()

        print("🚀 启动自动交易...")
        logger.info(f"启动自动交易，配置文件: {config_file_path}")

        # 检查配置文件
        if not Path(config_file_path).exists():
            print(f"❌ 配置文件不存在: {config_file_path}")
            logger.error(f"配置文件不存在: {config_file_path}")
            return
        
        # 加载自动交易配置
        print(f"📋 加载配置文件: {config_file_path}")
        auto_config = AutoBuyConfig.from_file(config_file_path)
        
        # 验证配置
        validation_errors = auto_config.validate()
        if validation_errors:
            print("❌ 配置验证失败:")
            for error in validation_errors:
                print(f"   - {error}")
            return
        
        print(f"✅ 配置验证通过: {auto_config.name}")
        print(f"📝 配置描述: {auto_config.description}")
        print(f"🎯 购买目标数量: {len(auto_config.targets)}")
        print(f"🔍 干运行模式: {auto_config.dry_run}")

        # 加载简单配置（优先使用AutoBuyConfig中的私钥）
        simple_config = load_config(auto_config)

        # 显示钱包信息和余额
        print(f"\n💰 钱包配置:")
        temp_buyer = PPPSimpleBuyer(simple_config.private_key, simple_config.rpc_url)
        wallet_address = temp_buyer.wallet_keypair.pubkey()
        print(f"   钱包地址: {wallet_address}")
        print(f"   RPC节点: {simple_config.rpc_url}")

        # 获取钱包余额
        try:
            async with temp_buyer:
                balance_response = await temp_buyer.client.get_balance(wallet_address)
                balance_sol = balance_response.value / 1_000_000_000
                print(f"   钱包余额: {balance_sol:.4f} SOL")
        except Exception as e:
            print(f"   钱包余额: 获取失败 ({e})")

        # 显示目标信息
        print(f"\n🎯 购买目标:")
        for i, target in enumerate(auto_config.targets, 1):
            print(f"   目标{i}: {target.project_name}")
            print(f"     项目mint: {target.mint_pubkey}")
            print(f"     目标NFT IDs: {target.target_nft_ids}")
            print(f"     最大数量: {target.max_quantity}")
        
        # 交互式确认
        if not auto_config.dry_run:
            print("\n⚠️  警告: 这将执行真实的NFT购买交易!")
            confirm = input("确认继续吗? (y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ 用户取消操作")
                return
        else:
            print("\n🔍 干运行模式，直接启动（无需确认）")
        
        # simple_config已经在上面加载过了
        
        # 创建组件实例
        async with PPPAPIClient() as api_client:
            project_manager = ProjectDataManager(api_client)
            
            async with PPPSimpleBuyer(simple_config.private_key, simple_config.rpc_url) as buyer:
                # 创建交易执行器
                trader = ConcurrentTrader(buyer, api_client)

                # 配置交易执行器（提供默认值）
                from auto_trading.models import ConcurrencySettings, PriorityFeeSettings, RetrySettings

                concurrency_settings = auto_config.concurrency_settings or ConcurrencySettings()
                priority_fee_settings = auto_config.priority_fee_settings or PriorityFeeSettings()
                retry_settings = auto_config.retry_settings or RetrySettings()

                trader.configure(
                    concurrency_settings,
                    priority_fee_settings,
                    retry_settings
                )

                # 创建调度器（传入trader以支持随机购买）
                scheduler = TradingScheduler(buyer, project_manager, trader)
                
                print("✅ 所有组件初始化完成")
                
                # 启动自动交易
                print("\n🚀 开始自动交易...")
                trading_result = await scheduler.start_auto_trading(auto_config)
                
                # 显示最终结果
                print("\n📊 自动交易完成!")
                print("=" * 50)
                print(f"配置名称: {trading_result['config_name']}")
                print(f"处理目标: {trading_result['targets_processed']}")
                print(f"总成功购买: {trading_result['total_successful_purchases']}")
                print(f"总失败购买: {trading_result['total_failed_purchases']}")
                
                if trading_result['errors']:
                    print(f"错误数量: {len(trading_result['errors'])}")
                    print("错误详情:")
                    for error in trading_result['errors'][:5]:  # 只显示前5个错误
                        print(f"   - {error}")
                    if len(trading_result['errors']) > 5:
                        print(f"   ... 还有 {len(trading_result['errors']) - 5} 个错误")
                
                print("=" * 50)
                
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {config_file_path}")
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
    except Exception as e:
        print(f"❌ 自动交易失败: {e}")
        import traceback
        traceback.print_exc()


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="PPP.fun 自动交易命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 创建新的自动交易配置
  python auto_trading_cli.py create-config

  # 启动自动交易
  python auto_trading_cli.py start auto_buy_configs/my_config.json

  # 显示帮助信息
  python auto_trading_cli.py --help
  python auto_trading_cli.py create-config --help
  python auto_trading_cli.py start --help

注意事项:
  - 首次使用建议先创建配置文件
  - 配置文件支持干运行模式，建议先测试
  - 确保config.json中包含正确的RPC和私钥配置
        """
    )

    subparsers = parser.add_subparsers(
        dest='command',
        help='可用命令',
        metavar='COMMAND'
    )

    # create-config 子命令
    create_parser = subparsers.add_parser(
        'create-config',
        help='创建新的自动交易配置',
        description='交互式创建自动交易配置文件'
    )

    # start 子命令
    start_parser = subparsers.add_parser(
        'start',
        help='启动自动交易',
        description='使用指定配置文件启动自动交易'
    )
    start_parser.add_argument(
        'config_file',
        help='自动交易配置文件路径'
    )

    # 检查是否没有提供任何参数
    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(0)

    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_arguments()

    # 如果是start命令，提前设置项目名称
    if args.command == 'start' and hasattr(args, 'config_file'):
        project_name = Path(args.config_file).stem
        set_project_name(project_name)

    print_banner()

    try:
        if args.command == 'create-config':
            await create_config_command()
        elif args.command == 'start':
            await start_trading_command(args.config_file)
        else:
            print(f"❌ 未知命令: {args.command}")

    except KeyboardInterrupt:
        print("\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 用户中断操作")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        sys.exit(1)
