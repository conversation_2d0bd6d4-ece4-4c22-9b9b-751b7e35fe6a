"""
PPP.fun 简化买入器配置管理
"""

import os
from pathlib import Path
from dataclasses import dataclass

# 尝试导入python-dotenv，如果没有安装则使用手动解析
try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

@dataclass
class SimpleConfig:
    """简化配置类"""
    private_key: str
    rpc_url: str = "https://api.mainnet-beta.solana.com"
    dry_run: bool = True
    max_retries: int = 3
    timeout: int = 30
    
    @classmethod
    def from_env(cls) -> 'SimpleConfig':
        """从环境变量和.env文件加载配置"""
        # 加载.env文件
        cls._load_env_file()

        private_key = os.getenv("WALLET_PRIVATE_KEY")
        if not private_key:
            raise ValueError("请设置环境变量 WALLET_PRIVATE_KEY 或在.env文件中设置")

        # 安全的类型转换
        try:
            max_retries = int(float(os.getenv("MAX_RETRIES", "3")))
        except (ValueError, TypeError):
            max_retries = 3

        try:
            timeout = int(float(os.getenv("TIMEOUT", "30")))
        except (ValueError, TypeError):
            timeout = 30

        return cls(
            private_key=private_key,
            rpc_url=os.getenv("RPC_URL", "https://api.mainnet-beta.solana.com"),
            dry_run=os.getenv("DRY_RUN", "true").lower() == "true",
            max_retries=max_retries,
            timeout=timeout
        )

    @staticmethod
    def _load_env_file():
        """加载.env文件"""
        env_file = Path(".env")
        if not env_file.exists():
            return

        if DOTENV_AVAILABLE:
            # 使用python-dotenv库，强制覆盖已存在的环境变量
            load_dotenv(env_file, override=True)
        else:
            # 手动解析.env文件
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('"').strip("'")
                        os.environ[key] = value
    


def load_config(auto_config=None) -> SimpleConfig:
    """智能加载配置

    Args:
        auto_config: AutoBuyConfig实例，如果提供且包含wallet_private_key，将优先使用
    """
    # 如果提供了auto_config且包含私钥，优先使用
    if auto_config and hasattr(auto_config, 'wallet_private_key') and auto_config.wallet_private_key:
        print(f"✅ 使用AutoBuyConfig中的私钥配置")

        # 尝试从环境变量获取其他配置作为默认值
        try:
            SimpleConfig._load_env_file()
            rpc_url = os.getenv("RPC_URL", "https://api.mainnet-beta.solana.com")
            dry_run = getattr(auto_config, 'dry_run', os.getenv("DRY_RUN", "true").lower() == "true")
            max_retries = int(float(os.getenv("MAX_RETRIES", "3")))
            timeout = int(float(os.getenv("TIMEOUT", "30")))
        except (ValueError, TypeError):
            # 如果环境变量解析失败，使用默认值
            rpc_url = "https://api.mainnet-beta.solana.com"
            dry_run = getattr(auto_config, 'dry_run', True)
            max_retries = 3
            timeout = 30

        return SimpleConfig(
            private_key=auto_config.wallet_private_key,
            rpc_url=rpc_url,
            dry_run=dry_run,
            max_retries=max_retries,
            timeout=timeout
        )

    # 从环境变量和.env文件加载
    try:
        config = SimpleConfig.from_env()
        return config
    except ValueError as env_error:
        # 环境变量加载失败
        print("❌ 配置加载失败")
        print("\n请按以下方式配置:")
        print("1. 创建 .env 文件:")
        print("   cp .env.example .env")
        print("   然后编辑 .env 文件设置 WALLET_PRIVATE_KEY")
        print("\n2. 设置环境变量:")
        print("   export WALLET_PRIVATE_KEY='your_base58_private_key'")
        print("   export RPC_URL='https://api.mainnet-beta.solana.com'")
        print("   export DRY_RUN='true'")
        print("\n3. 在AutoBuyConfig配置文件中设置 wallet_private_key 字段")
        print(f"\n错误原因: {env_error}")

        raise ValueError("请先配置私钥")

if __name__ == "__main__":
    # 测试配置加载
    try:
        config = SimpleConfig.from_env()
        print("✅ 配置加载成功")
        print(f"RPC URL: {config.rpc_url}")
        print(f"干运行模式: {config.dry_run}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
