{"address": "PPP5a1E95YMFv8Rmhvc7PBW6SSfHfvNKfAz7988xvB3", "metadata": {"name": "pppfun", "version": "0.1.0", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "burn", "discriminator": [116, 110, 29, 56, 107, 219, 42, 93], "accounts": [{"name": "nft", "writable": true}, {"name": "authority", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 112, 112, 95, 97, 117, 116, 104]}, {"kind": "account", "path": "project.mint", "account": "project"}]}}, {"name": "mint"}, {"name": "outputVault", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "authority"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "outputTokenAccount", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "payer"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "project", "writable": true}, {"name": "payer", "writable": true, "signer": true}, {"name": "tokenProgram", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associatedTokenProgram", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "systemProgram", "address": "11111111111111111111111111111111"}], "args": []}, {"name": "buy", "discriminator": [102, 6, 61, 18, 1, 218, 235, 234], "accounts": [{"name": "cpSwapProgram", "address": "CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C"}, {"name": "payer", "docs": ["The user performing the swap"], "writable": true, "signer": true}, {"name": "authority", "pda": {"seeds": [{"kind": "const", "value": [118, 97, 117, 108, 116, 95, 97, 110, 100, 95, 108, 112, 95, 109, 105, 110, 116, 95, 97, 117, 116, 104, 95, 115, 101, 101, 100]}], "program": {"kind": "account", "path": "cpSwapProgram"}}}, {"name": "ammConfig", "docs": ["The factory state to read protocol fees"]}, {"name": "poolState", "docs": ["The program account of the pool in which the swap will be performed"], "writable": true}, {"name": "inputTokenAccount", "docs": ["The user token account for input token"], "writable": true}, {"name": "outputTokenAccount", "docs": ["The user token account for output token"], "writable": true}, {"name": "inputVault", "docs": ["The vault token account for input token"], "writable": true}, {"name": "outputVault", "docs": ["The vault token account for output token"], "writable": true}, {"name": "inputTokenProgram", "docs": ["SPL program for input token transfers"]}, {"name": "outputTokenProgram", "docs": ["SPL program for output token transfers"]}, {"name": "inputTokenMint", "docs": ["The mint of input token"]}, {"name": "outputTokenMint", "docs": ["The mint of output token"]}, {"name": "observationState", "docs": ["The program account for the most recent oracle observation"], "writable": true}, {"name": "systemProgram", "docs": ["To create a new program account"], "address": "11111111111111111111111111111111"}, {"name": "tokenProgram", "docs": ["Program to create mint account and mint tokens"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "project", "writable": true}, {"name": "nft", "writable": true}, {"name": "current<PERSON>wner", "docs": ["CHECK Current owner of the NFT (must match nft.owner and be a system account)"], "writable": true}, {"name": "wsolMint", "address": "So11111111111111111111111111111111111111112"}, {"name": "poolWsolVault", "writable": true}, {"name": "poolAuth", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 112, 112, 95, 97, 117, 116, 104]}, {"kind": "account", "path": "project.mint", "account": "project"}]}}, {"name": "protocolFee", "writable": true, "address": "DFShmtUPnJaxKSX6wg2zbyWEAVs9HuBeKNeGihg8o2oV"}], "args": []}, {"name": "create", "discriminator": [24, 30, 200, 40, 5, 28, 7, 119], "accounts": [{"name": "newProject", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 112, 112, 95, 112, 114, 111, 106, 101, 99, 116]}, {"kind": "account", "path": "mint"}]}}, {"name": "authority", "pda": {"seeds": [{"kind": "const", "value": [112, 112, 112, 95, 97, 117, 116, 104]}, {"kind": "account", "path": "mint"}]}}, {"name": "metadata", "writable": true}, {"name": "tokenMetadataProgram", "address": "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"}, {"name": "mint", "writable": true, "signer": true}, {"name": "pool<PERSON><PERSON>Vault", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "authority"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "wsolMint", "address": "So11111111111111111111111111111111111111112"}, {"name": "poolWsolVault", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "authority"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "wsolMint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "payer", "writable": true, "signer": true}, {"name": "tokenProgram", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associatedTokenProgram", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "rent", "address": "SysvarRent111111111111111111111111111111111"}, {"name": "systemProgram", "address": "11111111111111111111111111111111"}], "args": [{"name": "projectName", "type": "string"}, {"name": "projectDesc", "type": "string"}, {"name": "tokenSymbol", "type": "string"}, {"name": "tokenUri", "type": "string"}, {"name": "initNftPrice", "type": "u64"}, {"name": "increasePerRound", "type": "u64"}, {"name": "tradeFeeRate", "type": "u64"}, {"name": "secPerRound", "type": "u64"}, {"name": "secToBurnNft", "type": "u64"}]}, {"name": "mint", "discriminator": [51, 57, 225, 47, 182, 146, 137, 166], "accounts": [{"name": "cpSwapProgram", "address": "CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C"}, {"name": "payer", "writable": true, "signer": true}, {"name": "creator", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 112, 112, 95, 97, 117, 116, 104]}, {"kind": "account", "path": "project.mint", "account": "project"}]}}, {"name": "ammConfig", "docs": ["Which config the pool belongs to."]}, {"name": "authority", "pda": {"seeds": [{"kind": "const", "value": [118, 97, 117, 108, 116, 95, 97, 110, 100, 95, 108, 112, 95, 109, 105, 110, 116, 95, 97, 117, 116, 104, 95, 115, 101, 101, 100]}], "program": {"kind": "account", "path": "cpSwapProgram"}}}, {"name": "poolState", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}, {"kind": "account", "path": "ammConfig"}, {"kind": "account", "path": "token0Mint"}, {"kind": "account", "path": "token1Mint"}], "program": {"kind": "account", "path": "cpSwapProgram"}}}, {"name": "token0Mint", "docs": ["Token_0 mint, the key must smaller then token_1 mint."]}, {"name": "token1Mint", "docs": ["Token_1 mint, the key must grater then token_0 mint."]}, {"name": "lpMint", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108, 95, 108, 112, 95, 109, 105, 110, 116]}, {"kind": "account", "path": "poolState"}], "program": {"kind": "account", "path": "cpSwapProgram"}}}, {"name": "creatorToken0", "docs": ["payer token0 account"], "writable": true}, {"name": "creatorToken1", "docs": ["creator token1 account"], "writable": true}, {"name": "creatorLpToken", "writable": true}, {"name": "token0Vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "poolState"}, {"kind": "account", "path": "token0Mint"}], "program": {"kind": "account", "path": "cpSwapProgram"}}}, {"name": "token1Vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "poolState"}, {"kind": "account", "path": "token1Mint"}], "program": {"kind": "account", "path": "cpSwapProgram"}}}, {"name": "createPoolFee", "docs": ["create pool fee account"], "writable": true, "address": "DNXgeM9EiiaAbaWvwjHj9fQQLAX5ZsfHyvmYUNRAdNC8"}, {"name": "observationState", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [111, 98, 115, 101, 114, 118, 97, 116, 105, 111, 110]}, {"kind": "account", "path": "poolState"}], "program": {"kind": "account", "path": "cpSwapProgram"}}}, {"name": "tokenProgram", "docs": ["Program to create mint account and mint tokens"], "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "token0Program", "docs": ["Spl token program or token program 2022"]}, {"name": "token1Program", "docs": ["Spl token program or token program 2022"]}, {"name": "associatedTokenProgram", "docs": ["Program to create an ATA for receiving position NFT"], "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "systemProgram", "docs": ["To create a new program account"], "address": "11111111111111111111111111111111"}, {"name": "rent", "docs": ["Sysvar for program account"], "address": "SysvarRent111111111111111111111111111111111"}, {"name": "newNft", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 112, 112, 95, 110, 102, 116]}, {"kind": "account", "path": "project.nftId", "account": "project"}, {"kind": "account", "path": "project"}]}}, {"name": "project", "writable": true}, {"name": "wsolMint", "address": "So11111111111111111111111111111111111111112"}, {"name": "poolWsolVault", "writable": true}, {"name": "protocolFee", "writable": true, "address": "DFShmtUPnJaxKSX6wg2zbyWEAVs9HuBeKNeGihg8o2oV"}], "args": []}, {"name": "split", "discriminator": [124, 189, 27, 43, 216, 40, 147, 66], "accounts": [{"name": "newNft", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 112, 112, 95, 110, 102, 116]}, {"kind": "account", "path": "project.nftId", "account": "project"}, {"kind": "account", "path": "project"}]}}, {"name": "oldNft", "writable": true}, {"name": "project", "writable": true}, {"name": "payer", "writable": true, "signer": true}, {"name": "systemProgram", "address": "11111111111111111111111111111111"}], "args": []}], "accounts": [{"name": "ammConfig", "discriminator": [218, 244, 33, 104, 203, 203, 43, 111]}, {"name": "nft", "discriminator": [97, 230, 6, 21, 131, 208, 111, 115]}, {"name": "observationState", "discriminator": [122, 174, 197, 53, 129, 9, 165, 132]}, {"name": "poolState", "discriminator": [247, 237, 227, 245, 215, 195, 222, 70]}, {"name": "project", "discriminator": [205, 168, 189, 202, 181, 247, 142, 19]}], "events": [{"name": "nftEvent", "discriminator": [243, 91, 119, 67, 253, 51, 91, 233]}, {"name": "projectCreateEvent", "discriminator": [19, 238, 138, 63, 2, 74, 6, 61]}], "errors": [{"code": 6000, "name": "nameTooLong", "msg": "Project name exceeds maximum length of 32 characters"}, {"code": 6001, "name": "symbolTooLong", "msg": "Token symbol exceeds maximum length of 10 characters"}, {"code": 6002, "name": "uriTooLong", "msg": "Metadata URI exceeds maximum length of 200 characters"}, {"code": 6003, "name": "descTooLong", "msg": "Project description exceeds maximum length of 256 characters"}, {"code": 6004, "name": "initNftPrice", "msg": "Initial NFT price must be at least 0.1 SOL"}, {"code": 6005, "name": "invalidNftPriceMultiple", "msg": "NFT price must be a multiple of 0.1 SOL"}, {"code": 6006, "name": "invalidIncreaseRate", "msg": "Price increase rate must be between 5.00% and 20.00%"}, {"code": 6007, "name": "invalidFeeRate", "msg": "Trading fee rate must be between 1% and 10%"}, {"code": 6008, "name": "increaseBelowFee", "msg": "Price increase rate must be greater than trading fee rate"}, {"code": 6009, "name": "notHourMultiple", "msg": "Time duration must be in full hours (multiples of 3600 seconds)"}, {"code": 6010, "name": "invalidRoundDuration", "msg": "Round duration must be between 1 hour and 3 days"}, {"code": 6011, "name": "invalidBurnDuration", "msg": "NFT burn timeout must be between 1 hour and 7 days"}, {"code": 6012, "name": "burnTooShort", "msg": "NFT burn timeout must be longer than round duration"}, {"code": 6013, "name": "invalidRound", "msg": "Only first round can mint NFT"}, {"code": 6014, "name": "nftSoldOut", "msg": "NFT sold out"}, {"code": 6015, "name": "insufficientFunds", "msg": "Insufficient swap pool funds after fees"}, {"code": 6016, "name": "fundsMismatch", "msg": "Funds distribution mismatch"}, {"code": 6017, "name": "invalidTokenMint", "msg": "Invalid token mint for mint 0"}, {"code": 6018, "name": "mathOverflow", "msg": "Math overflow in price calculation"}, {"code": 6019, "name": "invalidTokenPair", "msg": "Invalid token pair for minting"}, {"code": 6020, "name": "<PERSON><PERSON><PERSON><PERSON>", "msg": "Provided owner does not match the NFT's current owner"}, {"code": 6021, "name": "selfPurchaseNotAllowed", "msg": "Cannot purchase your own NFT"}, {"code": 6022, "name": "ownerMismatch", "msg": "Owner account mismatch"}, {"code": 6023, "name": "invalidOwnerAccount", "msg": "Owner must be a system account"}, {"code": 6024, "name": "mintingNotFinished", "msg": "Cannot trade until minting phase is complete"}, {"code": 6025, "name": "invalidMint", "msg": "NFT does not belong to this project"}, {"code": 6026, "name": "nftSoldOutThisRound", "msg": "NFT already traded this round. Wait for next round to trade again"}, {"code": 6027, "name": "nftNotSplitable", "msg": "NFT price must be at least 2x initial price to split"}, {"code": 6028, "name": "nftMaxSupplyReached", "msg": "Project has reached maximum NFT supply"}, {"code": 6029, "name": "invalidNewPrice", "msg": "Split price cannot be below initial NFT price"}, {"code": 6030, "name": "timeEclipseNotReachedSplit", "msg": "Must wait one full round before splitting again"}, {"code": 6031, "name": "timeEclipseNotReached", "msg": "Must wait for burn timeout period before burning NFT"}, {"code": 6032, "name": "nftAlreadyBurned", "msg": "NFT has already been burned"}], "types": [{"name": "ammConfig", "docs": ["Holds the current owner of the factory"], "type": {"kind": "struct", "fields": [{"name": "bump", "docs": ["Bump to identify PDA"], "type": "u8"}, {"name": "disableCreatePool", "docs": ["Status to control if new pool can be create"], "type": "bool"}, {"name": "index", "docs": ["Config index"], "type": "u16"}, {"name": "tradeFeeRate", "docs": ["The trade fee, denominated in hundredths of a bip (10^-6)"], "type": "u64"}, {"name": "protocolFeeRate", "docs": ["The protocol fee"], "type": "u64"}, {"name": "fundFeeRate", "docs": ["The fund fee, denominated in hundredths of a bip (10^-6)"], "type": "u64"}, {"name": "createPoolFee", "docs": ["Fee for create a new pool"], "type": "u64"}, {"name": "protocolOwner", "docs": ["Address of the protocol fee owner"], "type": "pubkey"}, {"name": "fundOwner", "docs": ["Address of the fund fee owner"], "type": "pubkey"}, {"name": "padding", "docs": ["padding"], "type": {"array": ["u64", 16]}}]}}, {"name": "nft", "type": {"kind": "struct", "fields": [{"name": "bump", "type": "u8"}, {"name": "mint", "type": "pubkey"}, {"name": "owner", "type": "pubkey"}, {"name": "id", "type": "u64"}, {"name": "round", "type": "u64"}, {"name": "price", "type": "u64"}, {"name": "splitCount", "type": "u64"}, {"name": "lastSplit", "type": "u64"}, {"name": "lastTrade", "type": "u64"}, {"name": "createTime", "type": "u64"}, {"name": "isBurned", "type": "bool"}]}}, {"name": "nftEvent", "docs": ["Emitted when NFT mint buy split burn"], "type": {"kind": "struct", "fields": [{"name": "mint", "type": "pubkey"}, {"name": "nft", "type": "pubkey"}, {"name": "id", "type": "u64"}, {"name": "aid", "type": "u64"}, {"name": "round", "type": "u64"}, {"name": "from", "type": "pubkey"}, {"name": "to", "type": "pubkey"}, {"name": "price", "type": "u64"}, {"name": "value", "type": "u64"}, {"name": "time", "type": "u64"}, {"name": "action", "type": "string"}]}}, {"name": "observation", "docs": ["The element of observations in ObservationState"], "serialization": "bytemuckunsafe", "repr": {"kind": "c", "packed": true}, "type": {"kind": "struct", "fields": [{"name": "blockTimestamp", "docs": ["The block timestamp of the observation"], "type": "u64"}, {"name": "cumulativeToken0PriceX32", "docs": ["the cumulative of token0 price during the duration time, Q32.32, the remaining 64 bit for overflow"], "type": "u128"}, {"name": "cumulativeToken1PriceX32", "docs": ["the cumulative of token1 price during the duration time, Q32.32, the remaining 64 bit for overflow"], "type": "u128"}]}}, {"name": "observationState", "serialization": "bytemuckunsafe", "repr": {"kind": "c", "packed": true}, "type": {"kind": "struct", "fields": [{"name": "initialized", "docs": ["Whether the ObservationState is initialized"], "type": "bool"}, {"name": "observationIndex", "docs": ["the most-recently updated index of the observations array"], "type": "u16"}, {"name": "poolId", "type": "pubkey"}, {"name": "observations", "docs": ["observation array"], "type": {"array": [{"defined": {"name": "observation"}}, 100]}}, {"name": "padding", "docs": ["padding for feature update"], "type": {"array": ["u64", 4]}}]}}, {"name": "poolState", "serialization": "bytemuckunsafe", "repr": {"kind": "c", "packed": true}, "type": {"kind": "struct", "fields": [{"name": "ammConfig", "docs": ["Which config the pool belongs"], "type": "pubkey"}, {"name": "poolCreator", "docs": ["pool creator"], "type": "pubkey"}, {"name": "token0Vault", "docs": ["Token A"], "type": "pubkey"}, {"name": "token1Vault", "docs": ["Token B"], "type": "pubkey"}, {"name": "lpMint", "docs": ["Pool tokens are issued when A or B tokens are deposited.", "Pool tokens can be withdrawn back to the original A or B token."], "type": "pubkey"}, {"name": "token0Mint", "docs": ["Mint information for token A"], "type": "pubkey"}, {"name": "token1Mint", "docs": ["Mint information for token B"], "type": "pubkey"}, {"name": "token0Program", "docs": ["token_0 program"], "type": "pubkey"}, {"name": "token1Program", "docs": ["token_1 program"], "type": "pubkey"}, {"name": "observation<PERSON>ey", "docs": ["observation account to store oracle data"], "type": "pubkey"}, {"name": "authBump", "type": "u8"}, {"name": "status", "docs": ["Bitwise representation of the state of the pool", "bit0, 1: disable deposit(vaule is 1), 0: normal", "bit1, 1: disable withdraw(vaule is 2), 0: normal", "bit2, 1: disable swap(vaul<PERSON> is 4), 0: normal"], "type": "u8"}, {"name": "lpMintDecimals", "type": "u8"}, {"name": "mint0Decimals", "docs": ["mint0 and mint1 decimals"], "type": "u8"}, {"name": "mint1Decimals", "type": "u8"}, {"name": "lpSupply", "docs": ["lp mint supply"], "type": "u64"}, {"name": "protocolFeesToken0", "docs": ["The amounts of token_0 and token_1 that are owed to the liquidity provider."], "type": "u64"}, {"name": "protocolFeesToken1", "type": "u64"}, {"name": "fundFeesToken0", "type": "u64"}, {"name": "fundFeesToken1", "type": "u64"}, {"name": "openTime", "docs": ["The timestamp allowed for swap in the pool."], "type": "u64"}, {"name": "padding", "docs": ["padding for future updates"], "type": {"array": ["u64", 32]}}]}}, {"name": "project", "type": {"kind": "struct", "fields": [{"name": "bump", "type": "u8"}, {"name": "creator", "type": "pubkey"}, {"name": "mint", "type": "pubkey"}, {"name": "authority", "type": "pubkey"}, {"name": "projectName", "type": "string"}, {"name": "projectDesc", "type": "string"}, {"name": "tokenSymbol", "type": "string"}, {"name": "tokenUri", "type": "string"}, {"name": "initTokenSupply", "type": "u64"}, {"name": "maxTokenSupply", "type": "u64"}, {"name": "initNftSupply", "type": "u64"}, {"name": "maxNftSupply", "type": "u64"}, {"name": "tokenPerNft", "type": "u64"}, {"name": "initNftPrice", "type": "u64"}, {"name": "increasePerRound", "type": "u64"}, {"name": "tradeFeeRate", "type": "u64"}, {"name": "secPerRound", "type": "u64"}, {"name": "secToBurnNft", "type": "u64"}, {"name": "nftId", "type": "u64"}, {"name": "aid", "type": "u64"}, {"name": "nftIssueCount", "type": "u64"}, {"name": "nftBurnCount", "type": "u64"}, {"name": "round", "type": "u64"}, {"name": "lastRound", "type": "u64"}, {"name": "createTime", "type": "u64"}]}}, {"name": "projectCreateEvent", "docs": ["Emitted when Project create"], "type": {"kind": "struct", "fields": [{"name": "project", "type": "pubkey"}, {"name": "creator", "type": "pubkey"}, {"name": "mint", "type": "pubkey"}, {"name": "authority", "type": "pubkey"}, {"name": "projectName", "type": "string"}, {"name": "initNftPrice", "type": "u64"}, {"name": "increasePerRound", "type": "u64"}, {"name": "tradeFeeRate", "type": "u64"}, {"name": "secPerRound", "type": "u64"}, {"name": "secToBurnNft", "type": "u64"}, {"name": "createTime", "type": "u64"}]}}], "constants": [{"name": "version", "type": "string", "value": "\"1.0.0\""}]}