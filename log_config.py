"""
PPP.fun 日志配置模块
统一配置loguru日志系统，支持控制台和文件输出
"""

import sys
from pathlib import Path
from datetime import datetime
from loguru import logger

# 全局项目名称变量
_current_project_name = None

def set_project_name(project_name: str):
    """设置当前项目名称"""
    global _current_project_name
    _current_project_name = project_name

def get_project_name() -> str:
    """获取当前项目名称"""
    global _current_project_name
    return _current_project_name or "default"


def setup_logging(
    log_level: str = "INFO",
    log_to_file: bool = True,
    log_dir: str = "logs",
    app_name: str = "ppp_buyer",
    project_name: str = None
):
    """
    配置loguru日志系统

    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
        log_to_file: 是否输出到文件
        log_dir: 日志文件目录
        app_name: 应用模块名称，用于日志文件命名
        project_name: 项目名称，用于日志目录命名
    """
    
    # 移除默认的控制台处理器
    logger.remove()
    
    # 添加控制台输出（带颜色）
    logger.add(
        sys.stderr,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    if log_to_file:
        # 确保日志目录存在，按项目分目录
        # 优先使用参数中的项目名称，然后是全局项目名称
        final_project_name = project_name or get_project_name()
        log_path = Path(log_dir) / final_project_name
        log_path.mkdir(parents=True, exist_ok=True)

        # 生成日志文件名（模块名_时间戳.log）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"{app_name}_{timestamp}.log"
        log_file_path = log_path / log_filename

        # 添加文件输出（详细格式）
        logger.add(
            str(log_file_path),
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | "
                   "{level: <8} | "
                   "{name}:{function}:{line} | "
                   "{message}",
            rotation="10 MB",  # 文件大小超过10MB时轮转
            retention="7 days",  # 保留7天的日志文件
            compression="zip",  # 压缩旧日志文件
            encoding="utf-8"
        )

        # 添加错误日志单独文件
        error_log_filename = f"{app_name}_errors_{timestamp}.log"
        error_log_file_path = log_path / error_log_filename

        logger.add(
            str(error_log_file_path),
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | "
                   "{level: <8} | "
                   "{name}:{function}:{line} | "
                   "{message}",
            rotation="5 MB",
            retention="30 days",  # 错误日志保留更长时间
            compression="zip",
            encoding="utf-8"
        )
        
        logger.debug(f"📝 日志配置完成")
        logger.debug(f"   控制台输出: 启用 (级别: {log_level})")
        logger.debug(f"   文件输出: {log_file_path}")
        logger.debug(f"   错误日志: {error_log_file_path}")
    else:
        logger.debug(f"📝 日志配置完成 (仅控制台输出, 级别: {log_level})")


def setup_module_logging(module_name: str, log_level: str = "INFO"):
    """
    为特定模块设置日志配置
    
    Args:
        module_name: 模块名称
        log_level: 日志级别
    """
    setup_logging(
        log_level=log_level,
        log_to_file=True,
        app_name=module_name
    )


def get_logger():
    """
    获取配置好的logger实例
    
    Returns:
        loguru.logger实例
    """
    return logger


# 自动交易专用日志配置
def setup_auto_trading_logging(config_file_path: str = None):
    """自动交易模块专用日志配置

    Args:
        config_file_path: 配置文件路径，用于提取项目名称
    """
    project_name = None
    if config_file_path:
        # 从配置文件路径提取项目名称
        project_name = Path(config_file_path).stem

    setup_logging(
        log_level="INFO",
        log_to_file=True,
        app_name="auto_trading",
        project_name=project_name
    )


# API客户端专用日志配置
def setup_api_logging():
    """API客户端专用日志配置"""
    setup_logging(
        log_level="INFO",
        log_to_file=True,
        app_name="ppp_api"
    )


# 买入器专用日志配置
def setup_buyer_logging():
    """买入器专用日志配置"""
    setup_logging(
        log_level="INFO",
        log_to_file=True,
        app_name="ppp_buyer"
    )


if __name__ == "__main__":
    # 测试日志配置
    setup_logging()
    logger.info("日志配置测试")
    logger.warning("这是一个警告")
    logger.error("这是一个错误")
