{"payer": "3L1YudkUzeVjMev5CEjHpPNiyrQJ4ffuYEjeaE2suTs3", "ammConfig": "D4FPEruKEHrG5TenZ2mpDGEfu1iUvTiqBxvpU8HLBvC2", "poolState": "HLKKAbCxLGC5JeSNSzQt4oZSjNRNRLUHkMTSQcrdz3KV", "inputTokenAccount": "HfLhgjWkAXjzfQ8NTb15VZGfpDeCsUjETgwjF9s5To5w", "outputTokenAccount": "DMFKtHEhZyevFvhJinFpjtt3Lhd67z7pwdXf6znZPyHa", "inputVault": "8EwPcqT5QDbvDb1rrdCMPXyPQJSReS1vUukcm253vGup", "outputVault": "GjndJL2Fqqj7rhDtY2K6LmwpMRP5Nx7WwSSK1xWXjiWs", "inputTokenProgram": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "outputTokenProgram": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "inputTokenMint": "So11111111111111111111111111111111111111112", "outputTokenMint": "7X7Qhq4eLmMVfkUyLxZheaQ9oSaGDazNpWFUJfsjSppp", "observationState": "5ew8VUhx3RgtUpr2iDFgmUqFVekV5n8veabxtShayWPs", "project": "7TsWWMDU6GWSbe7vTxAAn7BB9FZxP1KVSNSkrVwP2Erg", "nft": "e2xGPoMqpEyyA9mtUXKDP95AiF5rhLAsQyDKFjuLecF", "currentOwner": "SyRmBTN3raWigkxUwXb4UeJfxMN6jWSACcT9dSPpfDt", "poolWsolVault": "HfLhgjWkAXjzfQ8NTb15VZGfpDeCsUjETgwjF9s5To5w", "referral": "NutPmLZUbFx2ULw4pbb3wVqyq2wr7BZc9ZPKMgNhdny", "creator": "HXF4113xTv1nwSRDp8CcM5M24BW17PU56ygpm7kvvSeG"}