# Bark 推送通知模块使用指南

## 📋 概述

Bark 推送通知模块是一个完整功能的 iOS 推送通知解决方案，专为 PPP.fun 项目设计。该模块具有以下特点：

- ✅ **业务安全**: 推送失败不会影响主业务流程
- ✅ **异步执行**: 支持后台线程推送，不阻塞主程序
- ✅ **完整功能**: 支持 Bark 的所有高级功能
- ✅ **日志集成**: 与现有日志系统完美集成
- ✅ **错误隔离**: 所有推送错误都被安全捕获和记录

## 🚀 快速开始

### 1. 环境配置

在 `.env` 文件中添加 Bark 配置：

```bash
# Bark 推送密钥 (从 Bark App 中获取)
BARK_KEY=your_bark_key_here

# Bark 服务器地址 (可选，默认使用官方服务器)
BARK_SERVER_URL=https://api.day.app
```

### 2. 获取 Bark Key

1. 在 App Store 下载 Bark 应用
2. 打开应用，复制显示的推送地址
3. 从地址中提取 key 部分
   - 例如: `https://api.day.app/ABC123/...` 中的 `ABC123` 就是 key

### 3. 基础使用

```python
from bark_notifier import init_bark, bark_send, bark_success

# 初始化（只需要一次）
init_bark()

# 发送基础推送
bark_send("标题", "内容")

# 发送成功通知
bark_success("操作成功！")
```

## 📚 功能详解

### 基础推送功能

```python
from bark_notifier import bark_send, bark_success, bark_error, bark_warning, bark_info

# 基础推送
bark_send("Hello", "这是一条测试消息")

# 状态通知
bark_success("操作成功")
bark_error("发生错误")
bark_warning("警告信息")
bark_info("普通信息")
```

### 高级推送功能

```python
from bark_notifier import BarkNotifier, NotificationLevel, SoundType

notifier = BarkNotifier()

# 带跳转链接
notifier.send_with_url("点击查看", "点击跳转到网页", "https://example.com")

# 分组推送
notifier.send_grouped("交易通知", "这是交易相关通知", "trading")

# 自定义声音
notifier.send_with_sound("重要通知", "使用警报声音", SoundType.ALARM)

# 时效性通知（专注模式下显示）
notifier.send_time_sensitive("紧急", "专注模式下也会显示")

# 关键警报（忽略静音模式）
notifier.send_critical("严重警报", "忽略静音和勿扰模式")

# 持续响铃（30秒）
notifier.send_call("紧急通知", "持续响铃30秒")
```

### 交易集成

```python
from bark_notifier import bark_trading_result

# 交易成功通知
bark_trading_result(
    success=True,
    project_name="项目名称",
    nft_id="123",
    price=0.5
)

# 交易失败通知
bark_trading_result(
    success=False,
    project_name="项目名称",
    error_message="余额不足"
)
```

### 批量推送

```python
from bark_notifier import BarkNotifier, BarkMessage

notifier = BarkNotifier()

messages = [
    BarkMessage(title=f"消息 {i}", body=f"内容 {i}")
    for i in range(1, 4)
]

# 批量发送（带延迟避免频率限制）
results = notifier.send_batch(messages, delay=1.0)
```

### 模板系统

```python
from bark_notifier import BarkNotifier

notifier = BarkNotifier()

# 使用内置模板
notifier.send_template("success", {"message": "操作完成"})
notifier.send_template("error", {"message": "操作失败"})

# 交易模板
notifier.send_template("trading_success", {
    "project": "项目名",
    "nft_id": "123",
    "price": "0.5"
})
```

## ⚙️ 配置选项

### 初始化参数

```python
from bark_notifier import BarkNotifier

notifier = BarkNotifier(
    key="your_bark_key",              # Bark 密钥
    server_url="https://api.day.app", # 服务器地址
    timeout=30,                       # 请求超时（秒）
    max_retries=3,                    # 最大重试次数
    async_mode=True,                  # 异步模式（推荐）
    max_workers=3,                    # 异步工作线程数
    enable_logging=True,              # 启用日志
    log_level="INFO"                  # 日志级别
)
```

### 异步模式说明

- **默认启用**: 推送在后台线程执行，不阻塞主程序
- **业务安全**: 推送失败不会影响主业务流程
- **性能优化**: 多个推送可以并发执行

```python
# 强制同步发送
result = notifier.send_message(message, async_send=False)

# 强制异步发送
result = notifier.send_message(message, async_send=True)
```

## 🔒 安全特性

### 错误隔离

所有推送函数都使用安全包装器，确保：

- 推送失败不会抛出异常
- 错误信息被记录到日志
- 主业务流程不受影响

```python
# 这些函数永远不会抛出异常
bark_send("标题", "内容")
bark_success("成功")
bark_error("错误")
```

### 安全状态检查

```python
from bark_notifier import safe_bark_send, safe_bark_notify

# 只返回成功/失败状态
success = safe_bark_send("标题", "内容")
if success:
    print("推送发送成功")

# 根据级别自动选择函数
success = safe_bark_notify("error", "错误消息")
```

## 🔧 项目集成示例

### 在交易模块中集成

```python
from bark_notifier import init_bark, bark_trading_result, bark_error

class Trader:
    def __init__(self):
        # 初始化 Bark（如果配置了 key）
        try:
            init_bark()
            self.bark_enabled = True
        except:
            self.bark_enabled = False
    
    def buy_nft(self, project, nft_id, price):
        try:
            # 执行交易逻辑
            result = self._execute_trade(project, nft_id, price)
            
            # 发送成功通知（不会影响主流程）
            if self.bark_enabled:
                bark_trading_result(
                    success=True,
                    project_name=project,
                    nft_id=nft_id,
                    price=price
                )
            
            return result
            
        except Exception as e:
            # 发送错误通知（不会影响主流程）
            if self.bark_enabled:
                bark_trading_result(
                    success=False,
                    project_name=project,
                    error_message=str(e)
                )
            
            raise  # 重新抛出业务异常
```

### 在日志系统中集成

```python
from bark_notifier import bark_error
from loguru import logger

# 自定义日志处理器，发送关键错误到 Bark
class BarkErrorHandler:
    def write(self, message):
        if "ERROR" in message or "CRITICAL" in message:
            # 提取错误信息并发送到 Bark
            bark_error(f"系统错误: {message[:100]}...")

# 添加到 loguru
logger.add(BarkErrorHandler(), level="ERROR")
```

## 📱 Bark App 设置建议

1. **分组设置**: 在 Bark 应用中为不同类型的通知设置分组
2. **声音配置**: 为重要通知配置特殊声音
3. **通知历史**: 定期查看推送历史，确保重要消息不遗漏

## 🐛 故障排除

### 常见问题

1. **推送不到达**
   - 检查 BARK_KEY 是否正确
   - 确认网络连接正常
   - 查看日志中的错误信息

2. **推送延迟**
   - 检查是否启用了异步模式
   - 调整 max_workers 参数
   - 确认服务器响应时间

3. **频率限制**
   - 使用批量推送功能
   - 增加推送间隔
   - 考虑使用自建服务器

### 调试模式

```python
# 启用详细日志
from bark_notifier import BarkNotifier

notifier = BarkNotifier(
    log_level="DEBUG",
    enable_logging=True
)
```

## 📄 许可证

本模块遵循项目的开源许可证。
