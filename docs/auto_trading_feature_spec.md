# PPP.fun 自动交易功能技术规格

## 功能概述

实现一个自动交易系统，能够在指定时间自动购买PPP.fun平台的NFT。系统支持项目选择、数量配置、时间调度和容错处理。

## 核心功能模块

### 1. 项目信息管理模块

#### 1.1 项目数据获取
```python
class ProjectInfoManager:
    async def fetch_project_info(self, project_mint: str) -> ProjectInfo
    async def get_unlock_time(self, project_mint: str) -> datetime
    async def persist_project_data(self, project_info: ProjectInfo) -> None
    async def load_project_data(self, project_mint: str) -> ProjectInfo
```

#### 1.2 数据持久化
- 存储路径: `project_data/{project_mint}.json`
- 包含字段: 项目基本信息、sec_per_round、last_round
- NFT列表实时从API获取，不持久化

### 2. 自动交易调度模块

#### 2.1 时间调度器
```python
class TradingScheduler:
    def calculate_next_round_time(self, last_round: int, sec_per_round: int) -> datetime
    async def check_current_round_status(self, project_mint: str) -> bool
    async def wait_for_next_round(self, target_time: datetime) -> None
    async def start_auto_trading(self, config: AutoTradingConfig) -> None
```

#### 2.2 购买策略
- **指定ID策略**: 按配置的NFT ID列表顺序购买
- **随机策略**: 从可用NFT中随机选择购买
- **混合策略**: 优先指定ID，不足时随机补充

### 3. 交易执行模块

#### 3.1 批量购买器
```python
class AutoTrader:
    async def execute_trading_plan(self, plan: TradingPlan) -> TradingResult
    async def buy_nft_with_retry(self, nft_id: int, max_retries: int = 3) -> bool
    async def get_available_nfts(self, project_mint: str) -> List[NFTInfo]
```

#### 3.2 容错处理
- NFT已被购买: 跳过，继续下一个
- 网络错误: 重试机制（最多3次）
- 余额不足: 记录错误，停止购买
- 其他错误: 记录日志，继续流程

### 4. 配置管理模块

#### 4.1 配置文件结构
```json
{
    "project_mint": "9U1DCq89ybinSytPNn1sfDbn1iv3MGPhGoyV3BvPSppp",
    "project_name": "Project Name",
    "target_quantity": 5,
    "strategy": "mixed",
    "specific_nft_ids": [83, 84, 85],
    "dry_run": true,
    "retry_settings": {
        "max_retries_per_nft": 3,
        "retry_delay_seconds": 0,
        "total_timeout_minutes": 1
    },
    "priority_fee_settings": {
        "low_microlamports": 50000,
        "medium_microlamports": 550000,
        "default_level": "medium",
        "auto_adjust": true
    },
    "concurrency_settings": {
        "max_concurrent_transactions": 5,
        "transaction_interval_ms": 10,
        "use_staggered_sending": true
    }
}
```

#### 4.2 CLI配置生成器
```python
class ConfigGenerator:
    async def interactive_config_creation(self) -> AutoTradingConfig
    async def select_project_from_list(self) -> str
    def input_trading_parameters(self) -> dict
    def save_config_file(self, config: AutoTradingConfig, filename: str) -> None
```

## 数据结构定义

### ProjectInfo
```python
@dataclass
class ProjectInfo:
    mint: str
    name: str
    symbol: str
    sec_per_round: int
    last_round: int
    total_supply: int
    last_updated: datetime

    def calculate_next_round_time(self) -> datetime:
        """根据last_round和sec_per_round计算下一轮时间"""
        pass
```

### AutoTradingConfig
```python
@dataclass
class AutoTradingConfig:
    project_mint: str
    project_name: str
    target_quantity: int
    strategy: str  # "specific", "random", "mixed"
    specific_nft_ids: List[int]
    dry_run: bool
    retry_settings: RetrySettings
    priority_fee_settings: PriorityFeeSettings
    concurrency_settings: ConcurrencySettings

@dataclass
class PriorityFeeSettings:
    low_microlamports: int = 50000
    medium_microlamports: int = 550000
    default_level: str = "medium"  # "low" or "medium"
    auto_adjust: bool = True

@dataclass
class ConcurrencySettings:
    max_concurrent_transactions: int = 5
    transaction_interval_ms: int = 10
    use_staggered_sending: bool = True
```

### TradingResult
```python
@dataclass
class TradingResult:
    total_attempted: int
    successful_purchases: int
    failed_purchases: int
    purchased_nft_ids: List[int]
    total_cost_sol: Decimal
    execution_time_seconds: float
    errors: List[str]
```

## 工作流程

### 1. 配置阶段
```
用户启动CLI → 选择项目 → 输入参数 → 生成配置文件 → 保存配置
```

### 2. 准备阶段
```
加载配置 → 获取项目信息 → 计算下一轮时间 → 检查当前轮状态 → 检查钱包余额 → 准备交易参数
```

#### 轮次时间计算逻辑
```python
def calculate_next_round_time(last_round: int, sec_per_round: int) -> datetime:
    """
    根据项目的last_round和sec_per_round计算下一轮时间
    last_round: 上一轮的时间戳
    sec_per_round: 每轮间隔秒数
    """
    next_round_timestamp = last_round + sec_per_round
    return datetime.fromtimestamp(next_round_timestamp)

async def check_current_round_tradeable(project_mint: str) -> bool:
    """
    检查当前轮是否可以交易
    通过NFT的last_trade字段判断是否在上一轮中有交易
    如果有交易记录且时间在当前轮范围内，说明当前轮可以交易
    """
    nfts = await get_project_nfts(project_mint)
    current_time = datetime.now().timestamp()

    for nft in nfts:
        if nft.last_trade and nft.last_trade > (current_time - sec_per_round):
            return True  # 当前轮可以交易

    return False  # 需要等待下一轮
```

### 3. 等待阶段
```
检查当前轮状态 → 如果可交易则直接开始 → 否则计算下一轮倒计时 → 显示等待状态 → 准备交易执行
```

#### 启动时状态判断流程
```
启动程序
    ↓
获取项目信息(sec_per_round, last_round)
    ↓
计算下一轮时间 = last_round + sec_per_round
    ↓
检查NFT的last_trade字段
    ↓
如果有NFT在当前轮有交易记录
    ↓                    ↓
  立即开始交易        等待下一轮时间到达
```

### 4. 执行阶段
```
解锁时间到达 → 获取可用NFT列表 → 执行购买策略 → 处理结果 → 记录日志
```

## 文件结构

```
auto_trading/
├── __init__.py
├── project_manager.py      # 项目信息管理
├── scheduler.py           # 时间调度器
├── trader.py             # 交易执行器
├── config_generator.py   # 配置生成器
└── models.py            # 数据模型

configs/
├── auto_trading/
│   ├── project_83_config.json
│   └── project_105_config.json

project_data/
├── 9U1DCq89ybinSytPNn1sfDbn1iv3MGPhGoyV3BvPSppp.json
└── 7X7Qhq4eLmMVfkUyLxZheaQ9oSaGDazNpWFUJfsjSppp.json

logs/
└── auto_trading_20240101_120000.log
```

## CLI命令设计

### 创建配置
```bash
python auto_trading_cli.py create-config
```

### 启动自动交易
```bash
python auto_trading_cli.py start --config configs/auto_trading/project_83_config.json
```

### 查看项目信息
```bash
python auto_trading_cli.py project-info --mint 9U1DCq89ybinSytPNn1sfDbn1iv3MGPhGoyV3BvPSppp
```

## 错误处理策略

### 1. 网络错误
- 自动重试，指数退避
- 最大重试次数限制
- 超时后跳过当前操作

### 2. 交易错误
- 记录详细错误信息
- 区分可重试和不可重试错误
- 继续执行后续购买计划

### 3. 配置错误
- 启动时验证配置完整性
- 提供详细的错误提示
- 支持配置修复建议

## 并发交易实现方案

### 1. 并发控制策略
```python
class ConcurrencyManager:
    def __init__(self, max_concurrent: int = 5):
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.transaction_interval = 0.01  # 10毫秒间隔

    async def execute_concurrent_purchases(self, nft_ids: List[int]):
        tasks = []
        for i, nft_id in enumerate(nft_ids):
            # 错开发送时间，避免同时发送
            delay = i * self.transaction_interval
            task = self.purchase_with_delay(nft_id, delay)
            tasks.append(task)

        return await asyncio.gather(*tasks, return_exceptions=True)

    async def purchase_with_delay(self, nft_id: int, delay: float):
        await asyncio.sleep(delay)
        async with self.semaphore:
            return await self.purchase_nft_with_retry(nft_id)
```

### 2. 优先费用管理
```python
class PriorityFeeManager:
    def __init__(self, config: PriorityFeeSettings):
        self.config = config

    def get_priority_fee(self, urgency_level: str = None) -> int:
        if urgency_level == "urgent" or not urgency_level:
            return self.config.medium_microlamports  # 最高使用medium级别
        return self.config.low_microlamports

    def create_priority_fee_instruction(self, microlamports: int):
        return ComputeBudgetProgram.setComputeUnitPrice({
            "microLamports": microlamports
        })
```

### 3. 性能优化
- **并发限制**：最多5个同时进行的交易
- **错开发送**：每个交易间隔10毫秒发送
- **适度优先费用**：低级(50,000)和中级(550,000) microlamports
- **智能重试**：快速失败，避免阻塞其他交易
