#!/usr/bin/env python3
"""
NFT销毁管理器 - 处理批量销毁和自动监控业务逻辑
专注于业务逻辑，不涉及合约细节
"""

import asyncio
import time
from datetime import datetime
from typing import Optional, Dict, Any, List
from pathlib import Path

from ppp_simple_buyer import PPPSimpleBuyer
from ppp_api import PPPAPIClient
from log_config import get_logger

# 导入 Bark 推送模块
try:
    from bark_notifier import init_bark, safe_bark_notify_with_title
    BARK_AVAILABLE = True
except ImportError:
    BARK_AVAILABLE = False

logger = get_logger()

class NFTBurnManager:
    """NFT销毁管理器 - 处理业务逻辑"""
    
    def __init__(self, buyer: PPPSimpleBuyer):
        self.buyer = buyer
        self.wallet_address = str(buyer.wallet_keypair.pubkey())

        # 初始化 Bark 推送（如果可用）
        self.bark_enabled = False
        if BARK_AVAILABLE:
            try:
                init_bark()
                self.bark_enabled = True
                logger.info("🔔 Bark 推送已启用")
            except Exception as e:
                logger.warning(f"⚠️ Bark 推送初始化失败: {e}")
        else:
            logger.info("💡 Bark 推送不可用，将跳过推送通知")


    
    async def get_user_nfts_with_time_info(self) -> List[Dict[str, Any]]:
        """获取用户NFT并添加时间信息"""
        async with PPPAPIClient() as api_client:
            user_nfts = await api_client.get_user_nfts(self.wallet_address)
        
        # 添加时间信息
        current_time = int(time.time())
        burn_delay_seconds = 48 * 3600  # 48小时
        
        for nft in user_nfts:
            last_trade = nft.get('last_trade', 0)
            time_since_trade = current_time - last_trade
            remaining_time = burn_delay_seconds - time_since_trade
            
            nft['time_since_trade'] = time_since_trade
            nft['remaining_time'] = remaining_time
            nft['can_burn'] = remaining_time <= 0
            
            if nft['can_burn']:
                nft['time_info'] = "可立即销毁"
            else:
                remaining_hours = remaining_time / 3600
                nft['time_info'] = f"剩余 {remaining_hours:.1f}h"
        
        return user_nfts
    
    async def batch_burn_nfts(self, max_count: Optional[int] = None,
                             dry_run: bool = True,
                             force_all: bool = False) -> Dict[str, Any]:
        """
        批量销毁NFT

        Args:
            max_count: 最大销毁数量
            dry_run: 是否为干运行模式
            force_all: 是否强制销毁所有NFT（忽略48小时限制）
        """
        try:
            logger.info(f"🔥 开始批量销毁NFT (干运行: {dry_run}, 强制模式: {force_all})")

            # 获取用户的所有NFT
            user_nfts = await self.get_user_nfts_with_time_info()

            if not user_nfts:
                return {
                    "success": False,
                    "message": "未找到任何NFT",
                    "results": []
                }

            # 选择要销毁的NFT
            if force_all:
                # 强制模式：销毁所有NFT
                target_nfts = user_nfts
                logger.info(f"⚠️ 强制模式：将尝试销毁所有 {len(target_nfts)} 个NFT")
            else:
                # 正常模式：只销毁满足48小时条件的NFT
                target_nfts = [nft for nft in user_nfts if nft['can_burn']]

                if not target_nfts:
                    return {
                        "success": False,
                        "message": f"找到 {len(user_nfts)} 个NFT，但没有满足48小时销毁条件的NFT",
                        "results": []
                    }

                logger.info(f"📊 总NFT: {len(user_nfts)} 个，可销毁: {len(target_nfts)} 个")

            # 限制销毁数量
            if max_count and max_count > 0:
                target_nfts = target_nfts[:max_count]
                logger.info(f"🎯 限制销毁数量为 {max_count} 个")

            logger.info(f"📋 准备销毁 {len(target_nfts)} 个NFT")

            # 发送批量销毁开始通知
            mode_text = "模拟" if dry_run else "真实"
            if self.bark_enabled:
                safe_bark_notify_with_title(
                    "🔥 批量销毁开始",
                    f"开始{mode_text}销毁 {len(target_nfts)} 个 NFT",
                    "warning"
                )

            results = []
            success_count = 0
            failed_count = 0
            
            for i, nft in enumerate(target_nfts, 1):
                try:
                    logger.info(f"🔥 [{i}/{len(target_nfts)}] 销毁NFT #{nft['nft_id']} ({nft['token_symbol']}) - {nft['time_info']}")

                    burn_params = {
                        "nft_id": nft["nft_id"],
                        "project_mint": nft["project_mint"],
                        "dry_run": dry_run
                    }

                    result = await self.buyer.burn_nft(burn_params)
                    results.append({
                        "nft_id": nft["nft_id"],
                        "project_name": nft["project_name"],
                        "token_symbol": nft["token_symbol"],
                        "time_info": nft["time_info"],
                        "result": result
                    })

                    if result.get("success"):
                        success_count += 1
                        logger.info(f"✅ NFT #{nft['nft_id']} 销毁成功")
                    else:
                        failed_count += 1
                        logger.warning(f"❌ NFT #{nft['nft_id']} 销毁失败: {result.get('error_message')}")

                    # 添加延迟避免过快请求
                    if not dry_run and i < len(target_nfts):
                        await asyncio.sleep(1)
                        
                except Exception as e:
                    failed_count += 1
                    logger.error(f"❌ NFT #{nft['nft_id']} 销毁异常: {e}")
                    results.append({
                        "nft_id": nft["nft_id"],
                        "project_name": nft["project_name"],
                        "token_symbol": nft["token_symbol"],
                        "time_info": nft.get("time_info", "未知"),
                        "result": {"success": False, "error_message": str(e)}
                    })
            
            logger.info(f"🎉 批量销毁完成: 成功 {success_count}, 失败 {failed_count}")

            # 发送批量销毁完成通知
            mode_text = "模拟" if dry_run else "真实"
            if success_count > 0:
                if self.bark_enabled:
                    safe_bark_notify_with_title(
                        "🎉 批量销毁完成",
                        f"{mode_text}销毁完成: 成功 {success_count} 个, 失败 {failed_count} 个",
                        "success"
                    )
            else:
                if self.bark_enabled:
                    safe_bark_notify_with_title(
                        "⚠️ 批量销毁完成",
                        f"{mode_text}销毁完成: 全部失败 ({failed_count} 个)",
                        "warning"
                    )

            return {
                "success": True,
                "message": f"批量销毁完成: 成功 {success_count}, 失败 {failed_count}",
                "total_nfts": len(user_nfts),
                "target_nfts": len(target_nfts),
                "success_count": success_count,
                "failed_count": failed_count,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"❌ 批量销毁失败: {e}")
            return {
                "success": False,
                "message": f"批量销毁失败: {e}",
                "results": []
            }
    
    async def auto_burn_monitor(self, burn_delay_hours: int = 48,
                               check_interval_seconds: int = 1,
                               dry_run: bool = True) -> None:
        """自动销毁监控器"""
        logger.info(f"🔥 启动自动销毁监控器")
        logger.info(f"   钱包地址: {self.wallet_address}")
        logger.info(f"   销毁延迟: {burn_delay_hours} 小时")
        logger.info(f"   检测间隔: {check_interval_seconds} 秒")
        logger.info(f"   干运行模式: {dry_run}")
        
        burn_delay_seconds = burn_delay_hours * 3600
        monitoring_queue = set()  # 使用set避免重复
        
        # 只获取一次NFT列表
        logger.info("🔍 获取用户NFT列表...")
        user_nfts = await self.get_user_nfts_with_time_info()
        user_nfts_dict = {nft['nft_id']: nft for nft in user_nfts}
        
        logger.info(f"✅ 获取到 {len(user_nfts_dict)} 个NFT，开始监控")
        
        try:
            while len(user_nfts_dict) > 0:  # 当还有NFT需要监控时继续
                try:
                    current_time = int(time.time())

                    ready_to_burn = []
                    next_burn_time = None
                    next_nft_info = None

                    # 检查内存中的NFT列表
                    for nft_id, nft in list(user_nfts_dict.items()):
                        # 跳过已经处理过的NFT
                        if nft_id in monitoring_queue:
                            continue

                        last_trade = nft.get('last_trade', 0)
                        time_since_trade = current_time - last_trade

                        # 检查是否达到销毁条件
                        if time_since_trade >= burn_delay_seconds:
                            ready_to_burn.append(nft)
                            monitoring_queue.add(nft_id)

                            hours_since_trade = time_since_trade / 3600
                            logger.info(f"🎯 NFT #{nft_id} ({nft['token_symbol']}) 达到销毁条件")
                            logger.info(f"   距离上次交易: {hours_since_trade:.1f} 小时")
                        else:
                            remaining_time = burn_delay_seconds - time_since_trade
                            # 找到最近的销毁时间
                            if next_burn_time is None or remaining_time < next_burn_time:
                                next_burn_time = remaining_time
                                next_nft_info = nft

                    # 显示监控状态和倒计时
                    remaining_nfts = len(user_nfts_dict)
                    processed_nfts = len(monitoring_queue)

                    if next_burn_time is not None and next_nft_info:
                        remaining_hours = next_burn_time / 3600
                        remaining_minutes = (next_burn_time % 3600) / 60
                        remaining_seconds = next_burn_time % 60

                        if remaining_hours >= 1:
                            time_str = f"{remaining_hours:.1f}小时"
                        elif remaining_minutes >= 1:
                            time_str = f"{remaining_minutes:.0f}分{remaining_seconds:.0f}秒"
                        else:
                            time_str = f"{remaining_seconds:.0f}秒"

                        logger.info(f"📊 监控中: 剩余{remaining_nfts}个NFT | 最近销毁: NFT#{next_nft_info['nft_id']} 还需{time_str}")
                    else:
                        logger.info(f"📊 监控中: 剩余{remaining_nfts}个NFT | 已处理{processed_nfts}个")
                    
                    # 执行销毁
                    if ready_to_burn:
                        logger.info(f"🔥 准备销毁 {len(ready_to_burn)} 个NFT")

                        # 发送准备销毁通知
                        if len(ready_to_burn) == 1:
                            nft_info = ready_to_burn[0]
                            if self.bark_enabled:
                                safe_bark_notify_with_title(
                                    "🔥 NFT 准备销毁",
                                    f"NFT #{nft_info['nft_id']} ({nft_info['token_symbol']}) 即将销毁",
                                    "warning"
                                )
                        else:
                            if self.bark_enabled:
                                safe_bark_notify_with_title(
                                    "🔥 批量 NFT 准备销毁",
                                    f"准备销毁 {len(ready_to_burn)} 个 NFT",
                                    "warning"
                                )

                        for nft in ready_to_burn:
                            try:
                                logger.info(f"🔥 销毁NFT #{nft['nft_id']} ({nft['token_symbol']})")
                                
                                burn_params = {
                                    "nft_id": nft["nft_id"],
                                    "project_mint": nft["project_mint"],
                                    "dry_run": dry_run
                                }
                                
                                result = await self.buyer.burn_nft(burn_params)
                                
                                if result.get("success"):
                                    logger.info(f"✅ NFT #{nft['nft_id']} 销毁成功")

                                    # 发送销毁成功通知
                                    mode_text = "模拟" if dry_run else "真实"
                                    if self.bark_enabled:
                                        safe_bark_notify_with_title(
                                            "✅ NFT 销毁成功",
                                            f"NFT #{nft['nft_id']} ({nft['token_symbol']}) {mode_text}销毁成功",
                                            "success"
                                        )

                                    # 从监控队列和内存字典中移除
                                    monitoring_queue.discard(nft['nft_id'])
                                    user_nfts_dict.pop(nft['nft_id'], None)

                                    if not dry_run:
                                        # 真实销毁后添加延迟
                                        await asyncio.sleep(1)
                                else:
                                    logger.warning(f"❌ NFT #{nft['nft_id']} 销毁失败: {result.get('error_message')}")
                                    
                            except Exception as e:
                                logger.error(f"❌ NFT #{nft['nft_id']} 销毁异常: {e}")
                    

                    
                    # 检查是否所有NFT都已处理完毕
                    if len(user_nfts_dict) == 0:
                        logger.info("🎉 所有NFT都已处理完毕，停止监控")
                        break
                    
                    # 等待下次检测
                    await asyncio.sleep(check_interval_seconds)
                    
                except KeyboardInterrupt:
                    logger.info("⏹️ 收到中断信号，停止监控")
                    break
                except Exception as e:
                    logger.error(f"❌ 监控循环异常: {e}")
                    await asyncio.sleep(check_interval_seconds)
                    
        except Exception as e:
            logger.error(f"❌ 自动销毁监控器失败: {e}")
        finally:
            logger.info("🛑 自动销毁监控器已停止")
