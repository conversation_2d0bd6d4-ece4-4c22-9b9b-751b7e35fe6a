"""
OKX Token卖出器 - 封装OKX DEX API功能
"""

import asyncio
import time
import hmac
import hashlib
import base64
import base58
import aiohttp
from typing import Optional, Dict, Any
from solana.rpc.async_api import AsyncClient
from solana.rpc.types import TxOpts
from solana.rpc.commitment import Confirmed
from solders.transaction import VersionedTransaction
from solders.pubkey import Pubkey
from solders.keypair import Keypair
from spl.token.instructions import get_associated_token_address
from log_config import get_logger

logger = get_logger()

class OKXTokenSeller:
    """OKX Token卖出器"""
    
    def __init__(self, wallet_keypair: Keypair, rpc_url: str = "https://api.mainnet-beta.solana.com"):
        self.wallet_keypair = wallet_keypair
        self.rpc_url = rpc_url
        
        # OKX API配置
        self.okx_api_key = "d6505749-195c-426c-82c8-d770b812d0cd"
        self.okx_secret_key = "C6DECE24477D45003DF5CDEEE0AC9C72"
        self.okx_passphrase = "duEiMEUme9FQsq9."
        self.okx_project_id = "ani"
        self.okx_base_url = "https://web3.okx.com"
        
        # Solana配置
        self.wsol_mint = "So11111111111111111111111111111111111111112"
        self.chain_id = "501"
    
    def _generate_okx_headers(self, timestamp: str, method: str, request_path: str, query_string: str = "") -> Dict[str, str]:
        """生成OKX API请求头"""
        string_to_sign = timestamp + method + request_path + query_string
        signature = base64.b64encode(
            hmac.new(
                self.okx_secret_key.encode('utf-8'),
                string_to_sign.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        
        return {
            "Content-Type": "application/json",
            "OK-ACCESS-KEY": self.okx_api_key,
            "OK-ACCESS-SIGN": signature,
            "OK-ACCESS-TIMESTAMP": timestamp,
            "OK-ACCESS-PASSPHRASE": self.okx_passphrase,
            "OK-ACCESS-PROJECT": self.okx_project_id,
        }
    
    async def get_token_balance(self, token_mint: str) -> int:
        """获取指定token的余额"""
        client = None
        try:
            client = AsyncClient(self.rpc_url, commitment=Confirmed)
            
            # 计算关联token账户地址
            token_account = get_associated_token_address(
                self.wallet_keypair.pubkey(), 
                Pubkey.from_string(token_mint)
            )
            
            # 获取token账户余额
            balance_response = await client.get_token_account_balance(token_account)
            
            if balance_response.value is None:
                return 0
            
            balance = int(balance_response.value.amount)
            return balance
            
        except Exception as e:
            logger.error(f"❌ 获取token余额失败: {e}")
            return 0
        finally:
            if client:
                await client.close()
    
    async def sell_token(self, token_mint: str, amount: int, slippage_percent: float = 50.0) -> Dict[str, Any]:
        """卖出指定token"""
        try:
            # 转换滑点为小数形式
            slippage = slippage_percent / 100
            
            params = {
                "chainId": self.chain_id,
                "fromTokenAddress": token_mint,
                "toTokenAddress": self.wsol_mint,
                "amount": str(amount),
                "slippage": str(slippage),
                "userWalletAddress": str(self.wallet_keypair.pubkey())
            }
            
            timestamp = str(int(time.time() * 1000))
            request_path = "/api/v5/dex/aggregator/swap"
            query_string = "?" + "&".join([f"{k}={v}" for k, v in params.items()])
            
            headers = self._generate_okx_headers(timestamp, "GET", request_path, query_string)
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.okx_base_url}{request_path}{query_string}"
                async with session.get(url, headers=headers) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"❌ OKX Swap API错误: {response.status} - {error_text}")
                        return {"success": False, "error_message": f"API错误: {response.status}"}
                    
                    response_data = await response.json()
                    
                    if response_data.get("code") != "0":
                        logger.error(f"❌ OKX Swap API返回错误: {response_data.get('msg', 'Unknown error')}")
                        return {"success": False, "error_message": response_data.get('msg', 'Unknown error')}
                    
                    swap_data = response_data.get("data", [])
                    if not swap_data:
                        logger.error("❌ OKX Swap API返回空数据")
                        return {"success": False, "error_message": "API返回空数据"}
                    
                    # 执行交易
                    return await self._execute_transaction(swap_data[0])
                    
        except Exception as e:
            logger.error(f"❌ OKX卖出失败: {e}")
            return {"success": False, "error_message": f"卖出失败: {e}"}
    
    async def _execute_transaction(self, swap_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行OKX交易"""
        client = None
        try:
            client = AsyncClient(self.rpc_url, commitment=Confirmed)
            
            tx_data = swap_data.get("tx", {})
            signature_data_list = tx_data.get("signatureData", [])
            data_field = tx_data.get("data", "")
            
            # 如果signatureData为空，使用data字段
            if not signature_data_list and data_field:
                signature_data_list = [data_field]
            elif not any(signature_data_list) and data_field:
                signature_data_list = [data_field]
            
            if not signature_data_list:
                return {"success": False, "error_message": "没有找到交易数据"}
            
            for signature_data in signature_data_list:
                if not signature_data:
                    continue
                
                try:
                    # 解码交易数据
                    try:
                        transaction_bytes = base64.b64decode(signature_data)
                    except Exception:
                        transaction_bytes = base58.b58decode(signature_data)
                    
                    transaction = VersionedTransaction.from_bytes(transaction_bytes)
                    
                    # 更新blockhash
                    latest_blockhash = await client.get_latest_blockhash()
                    new_blockhash = latest_blockhash.value.blockhash
                    
                    message = transaction.message
                    from solders.message import MessageV0
                    if hasattr(message, 'header') and hasattr(message, 'address_table_lookups'):
                        new_message = MessageV0(
                            header=message.header,
                            account_keys=message.account_keys,
                            recent_blockhash=new_blockhash,
                            instructions=message.instructions,
                            address_table_lookups=message.address_table_lookups
                        )
                    else:
                        new_message = message
                    
                    # 签名并发送交易
                    signed_transaction = VersionedTransaction(new_message, [self.wallet_keypair])
                    opts = TxOpts(skip_confirmation=False, preflight_commitment=Confirmed)
                    serialized_tx = bytes(signed_transaction)
                    response = await client.send_raw_transaction(serialized_tx, opts)
                    
                    if response.value:
                        tx_sig = str(response.value)
                        logger.info(f"✅ Token卖出交易成功: {tx_sig}")
                        return {"success": True, "transaction_signature": tx_sig}
                    else:
                        return {"success": False, "error_message": "交易发送失败"}
                        
                except Exception as e:
                    logger.error(f"❌ 处理交易失败: {e}")
                    continue
            
            return {"success": False, "error_message": "所有交易都失败"}
            
        except Exception as e:
            logger.error(f"❌ 执行交易失败: {e}")
            return {"success": False, "error_message": f"执行失败: {e}"}
        finally:
            if client:
                await client.close()
    
    async def sell_all_tokens(self, token_mint: str, slippage_percent: float = 50.0) -> Dict[str, Any]:
        """卖出指定token的全部余额"""
        try:
            # 获取余额
            balance = await self.get_token_balance(token_mint)
            if balance == 0:
                return {"success": False, "error_message": "Token余额为0"}
            
            # 卖出全部
            return await self.sell_token(token_mint, balance, slippage_percent)
            
        except Exception as e:
            logger.error(f"❌ 卖出全部token失败: {e}")
            return {"success": False, "error_message": f"卖出失败: {e}"}
