"""
PPP.fun 自动交易数据模型
基于现有auto_buy_configs配置文件格式，保持向后兼容性
"""

import json
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field
from decimal import Decimal


@dataclass
class AutoBuyTarget:
    """自动购买目标配置"""
    project_id: str
    project_name: str
    mint_pubkey: str
    last_round: int
    sec_per_round: int
    target_nft_ids: List[int] = field(default_factory=list)
    max_quantity: int = 1
    creator: Optional[str] = None  # 项目creator地址，在配置加载时获取
    
    def calculate_next_round_time(self) -> datetime:
        """根据last_round和sec_per_round计算下一轮时间"""
        next_round_timestamp = self.last_round + self.sec_per_round
        return datetime.fromtimestamp(next_round_timestamp)


@dataclass
class ScheduleConfig:
    """调度配置"""
    use_api_time: bool = True
    countdown_seconds: int = 5
    batch_mode: bool = False
    batch_size: int = 1
    interval_ms: int = 10


@dataclass
class RetrySettings:
    """重试设置"""
    max_retries_per_nft: int = 3
    retry_delay_seconds: int = 0
    total_timeout_minutes: int = 1


@dataclass
class PriorityFeeSettings:
    """优先费用设置"""
    low_microlamports: int = 50000
    medium_microlamports: int = 550000
    default_level: str = "medium"  # "low" or "medium"
    auto_adjust: bool = True


@dataclass
class ConcurrencySettings:
    """并发设置"""
    max_concurrent_transactions: int = 5
    transaction_interval_ms: int = 10
    use_staggered_sending: bool = True


@dataclass
class AutoBuyConfig:
    """自动购买配置"""
    name: str
    description: str
    targets: List[AutoBuyTarget]
    schedule: ScheduleConfig
    wallet_private_key: Optional[str] = None
    enabled: bool = True
    created_at: Optional[str] = None
    
    # 新增字段，支持技术规格中的配置
    dry_run: bool = True
    retry_settings: Optional[RetrySettings] = None
    priority_fee_settings: Optional[PriorityFeeSettings] = None
    concurrency_settings: Optional[ConcurrencySettings] = None
    
    def __post_init__(self):
        """初始化后处理，设置默认值"""
        if self.retry_settings is None:
            self.retry_settings = RetrySettings()
        if self.priority_fee_settings is None:
            self.priority_fee_settings = PriorityFeeSettings()
        if self.concurrency_settings is None:
            self.concurrency_settings = ConcurrencySettings()
    
    @classmethod
    def from_file(cls, config_path: str) -> 'AutoBuyConfig':
        """从配置文件加载配置，复用SimpleConfig的加载模式"""
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件JSON格式错误: {e}")
        except Exception as e:
            raise ValueError(f"读取配置文件失败: {e}")
        
        # 验证必需字段
        required_fields = ["name", "description", "targets"]
        for field in required_fields:
            if field not in data:
                raise ValueError(f"配置文件缺少必需字段: {field}")
        
        # 转换targets
        targets = []
        for target_data in data["targets"]:
            # 验证target必需字段
            target_required = ["project_id", "project_name", "mint_pubkey"]
            for field in target_required:
                if field not in target_data:
                    raise ValueError(f"target配置缺少必需字段: {field}")
            
            target = AutoBuyTarget(
                project_id=target_data["project_id"],
                project_name=target_data["project_name"],
                mint_pubkey=target_data["mint_pubkey"],
                last_round=target_data.get("last_round", 0),
                sec_per_round=target_data.get("sec_per_round", 3600),
                target_nft_ids=target_data.get("target_nft_ids", []),
                max_quantity=target_data.get("max_quantity", 1),
                creator=target_data.get("creator")  # 从配置文件读取creator，如果没有则为None
            )
            targets.append(target)
        
        # 转换schedule配置
        schedule_data = data.get("schedule", {})
        schedule = ScheduleConfig(
            use_api_time=schedule_data.get("use_api_time", True),
            countdown_seconds=schedule_data.get("countdown_seconds", 5),
            batch_mode=schedule_data.get("batch_mode", False),
            batch_size=schedule_data.get("batch_size", 1),
            interval_ms=schedule_data.get("interval_ms", 100)
        )
        
        # 转换重试设置（如果存在）
        retry_settings = None
        if "retry_settings" in data:
            retry_data = data["retry_settings"]
            retry_settings = RetrySettings(
                max_retries_per_nft=retry_data.get("max_retries_per_nft", 3),
                retry_delay_seconds=retry_data.get("retry_delay_seconds", 0),
                total_timeout_minutes=retry_data.get("total_timeout_minutes", 1)
            )
        
        # 转换优先费用设置（如果存在）
        priority_fee_settings = None
        if "priority_fee_settings" in data:
            fee_data = data["priority_fee_settings"]
            priority_fee_settings = PriorityFeeSettings(
                low_microlamports=fee_data.get("low_microlamports", 50000),
                medium_microlamports=fee_data.get("medium_microlamports", 550000),
                default_level=fee_data.get("default_level", "medium"),
                auto_adjust=fee_data.get("auto_adjust", True)
            )
        
        # 转换并发设置（如果存在）
        concurrency_settings = None
        if "concurrency_settings" in data:
            conc_data = data["concurrency_settings"]
            concurrency_settings = ConcurrencySettings(
                max_concurrent_transactions=conc_data.get("max_concurrent_transactions", 5),
                transaction_interval_ms=conc_data.get("transaction_interval_ms", 10),
                use_staggered_sending=conc_data.get("use_staggered_sending", True)
            )
        
        config = cls(
            name=data["name"],
            description=data["description"],
            targets=targets,
            schedule=schedule,
            wallet_private_key=data.get("wallet_private_key"),
            enabled=data.get("enabled", True),
            created_at=data.get("created_at"),
            dry_run=data.get("dry_run", True),
            retry_settings=retry_settings,
            priority_fee_settings=priority_fee_settings,
            concurrency_settings=concurrency_settings
        )

        return config


    def save_to_file(self, config_path: str):
        """保存配置到文件"""
        config_file = Path(config_path)
        
        # 转换为字典格式
        config_data = {
            "name": self.name,
            "description": self.description,
            "targets": [
                {
                    "project_id": target.project_id,
                    "project_name": target.project_name,
                    "mint_pubkey": target.mint_pubkey,
                    "last_round": target.last_round,
                    "sec_per_round": target.sec_per_round,
                    "target_nft_ids": target.target_nft_ids,
                    "max_quantity": target.max_quantity,
                    "creator": target.creator
                }
                for target in self.targets
            ],
            "schedule": {
                "use_api_time": self.schedule.use_api_time,
                "countdown_seconds": self.schedule.countdown_seconds,
                "batch_mode": self.schedule.batch_mode,
                "batch_size": self.schedule.batch_size,
                "interval_ms": self.schedule.interval_ms
            },
            "wallet_private_key": self.wallet_private_key,
            "enabled": self.enabled,
            "created_at": self.created_at or datetime.now().isoformat(),
            "dry_run": self.dry_run
        }
        
        # 添加可选配置
        if self.retry_settings:
            config_data["retry_settings"] = {
                "max_retries_per_nft": self.retry_settings.max_retries_per_nft,
                "retry_delay_seconds": self.retry_settings.retry_delay_seconds,
                "total_timeout_minutes": self.retry_settings.total_timeout_minutes
            }
        
        if self.priority_fee_settings:
            config_data["priority_fee_settings"] = {
                "low_microlamports": self.priority_fee_settings.low_microlamports,
                "medium_microlamports": self.priority_fee_settings.medium_microlamports,
                "default_level": self.priority_fee_settings.default_level,
                "auto_adjust": self.priority_fee_settings.auto_adjust
            }
        
        if self.concurrency_settings:
            config_data["concurrency_settings"] = {
                "max_concurrent_transactions": self.concurrency_settings.max_concurrent_transactions,
                "transaction_interval_ms": self.concurrency_settings.transaction_interval_ms,
                "use_staggered_sending": self.concurrency_settings.use_staggered_sending
            }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    def validate(self) -> List[str]:
        """验证配置有效性，返回错误列表"""
        errors = []
        
        if not self.name.strip():
            errors.append("配置名称不能为空")
        
        if not self.description.strip():
            errors.append("配置描述不能为空")
        
        if not self.targets:
            errors.append("至少需要一个购买目标")
        
        for i, target in enumerate(self.targets):
            if not target.project_id.strip():
                errors.append(f"目标{i+1}的project_id不能为空")
            if not target.mint_pubkey.strip():
                errors.append(f"目标{i+1}的mint_pubkey不能为空")
            if target.max_quantity <= 0:
                errors.append(f"目标{i+1}的max_quantity必须大于0")
            if target.sec_per_round <= 0:
                errors.append(f"目标{i+1}的sec_per_round必须大于0")
        
        return errors
    
    def get_primary_target(self) -> Optional[AutoBuyTarget]:
        """获取主要购买目标（第一个目标）"""
        return self.targets[0] if self.targets else None
