"""
PPP.fun 自动交易配置生成工具
提供交互式配置生成功能，集成现有PPPAPIClient获取项目列表
"""

import asyncio
import json
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any

# 日志配置
import sys
sys.path.append('..')
from log_config import setup_logging, get_logger

# 初始化日志（使用全局项目名称）
setup_logging(app_name="config_generator")
logger = get_logger()

from .models import AutoBuyConfig, AutoBuyTarget, ScheduleConfig, RetrySettings, PriorityFeeSettings, ConcurrencySettings
from ppp_api import PPPAPIClient, PPPProject


class ConfigGenerator:
    """自动交易配置生成器"""
    
    def __init__(self, api_client: PPPAPIClient):
        """初始化配置生成器
        
        Args:
            api_client: PPPAPIClient实例，复用现有项目数据获取功能
        """
        self.api_client = api_client
        self.config_dir = Path("auto_buy_configs")
        self.config_dir.mkdir(exist_ok=True)
        logger.info("📝 配置生成器初始化完成")
    
    async def get_available_projects_with_rounds(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取可用的项目列表（包含完整的原始数据和轮次信息）

        Args:
            limit: 获取项目数量限制

        Returns:
            项目原始数据列表（包含轮次信息）
        """
        try:
            logger.info(f"🔍 获取项目列表，限制数量: {limit}")

            # 直接调用原始API获取完整数据
            params = {
                "limit": limit,
                "page": 1,
                "sort": "volume"
            }

            data = await self.api_client._request("GET", "/projects", params=params)
            projects_data = data.get("data", [])

            logger.info(f"✅ 成功获取 {len(projects_data)} 个项目")
            return projects_data
        except Exception as e:
            logger.error(f"❌ 获取项目列表失败: {e}")
            return []
    
    def display_projects_list(self, projects_data: List[Dict[str, Any]]) -> None:
        """显示项目列表供用户选择

        Args:
            projects_data: 项目原始数据列表
        """
        print("\n📋 可用项目列表:")
        print("=" * 130)
        print(f"{'序号':<4} {'项目名称':<18} {'符号':<8} {'地板价(SOL)':<10} {'24h成交量(SOL)':<12} {'下一轮时间':<25}")
        print("-" * 130)

        for i, project_data in enumerate(projects_data, 1):
            # 提取项目信息
            name = project_data.get("project_name", "Unknown")
            symbol = project_data.get("token_symbol", "Unknown")

            # 截断过长的名称
            name = name[:16] + ".." if len(name) > 18 else name
            symbol = symbol[:6] + ".." if len(symbol) > 8 else symbol

            # 计算地板价和成交量
            floor_price_lamports = project_data.get("init_nft_price", 0) or 0
            floor_price_sol = floor_price_lamports / (10**9)

            volume_24h_lamports = project_data.get("volume_24h", 0) or 0
            volume_24h_sol = volume_24h_lamports / (10**9)

            # 计算下一轮时间
            next_round_info = self._calculate_next_round_info(project_data)

            print(f"{i:<4} {name:<18} {symbol:<8} {floor_price_sol:<10.4f} {volume_24h_sol:<12.4f} {next_round_info:<25}")

        print("=" * 130)

    def _calculate_next_round_info(self, project_data: Dict[str, Any]) -> str:
        """从项目原始数据计算下一轮时间信息

        Args:
            project_data: 项目原始数据

        Returns:
            下一轮时间的格式化字符串
        """
        try:
            # 从原始数据中提取轮次信息
            last_round = project_data.get("last_round", 0)
            sec_per_round = project_data.get("sec_per_round", 3600)

            if not last_round or not sec_per_round:
                return "无轮次信息"

            # 计算下一轮时间
            next_round_timestamp = last_round + sec_per_round
            next_round_time = datetime.fromtimestamp(next_round_timestamp)
            current_time = datetime.now()

            # 计算时间差
            time_diff = (next_round_time - current_time).total_seconds()

            if time_diff <= 0:
                return "可立即交易"
            elif time_diff < 300:  # 小于5分钟，显示秒
                minutes = int(time_diff // 60)
                seconds = int(time_diff % 60)
                if minutes > 0:
                    return f"{minutes}分{seconds}秒后"
                else:
                    return f"{seconds}秒后"
            elif time_diff < 3600:  # 小于1小时，显示分钟
                minutes = int(time_diff // 60)
                return f"{minutes}分钟后"
            elif time_diff < 86400:  # 小于1天，显示小时分钟
                hours = int(time_diff // 3600)
                minutes = int((time_diff % 3600) // 60)
                return f"{hours}小时{minutes}分钟后"
            else:  # 大于1天，显示天数小时
                days = int(time_diff // 86400)
                hours = int((time_diff % 86400) // 3600)
                return f"{days}天{hours}小时后"

        except Exception as e:
            logger.debug(f"计算轮次时间失败: {e}")
            return "计算失败"

    def select_project_from_list(self, projects_data: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """从项目列表中选择项目

        Args:
            projects_data: 项目原始数据列表

        Returns:
            选择的项目数据，如果取消则返回None
        """
        if not projects_data:
            print("❌ 没有可用的项目")
            return None

        self.display_projects_list(projects_data)
        
        while True:
            try:
                choice = input(f"\n请选择项目 (1-{len(projects_data)}, 0=取消): ").strip()

                if choice == "0":
                    print("❌ 用户取消选择")
                    return None

                index = int(choice) - 1
                if 0 <= index < len(projects_data):
                    selected_project = projects_data[index]
                    project_name = selected_project.get("project_name", "Unknown")
                    project_symbol = selected_project.get("token_symbol", "Unknown")
                    print(f"✅ 已选择项目: {project_name} ({project_symbol})")
                    return selected_project
                else:
                    print(f"❌ 无效选择，请输入 1-{len(projects_data)} 之间的数字")

            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                print("\n❌ 用户中断操作")
                raise KeyboardInterrupt("用户中断配置过程")
    
    def input_with_default(self, prompt: str, default_value: Any, value_type: type = str) -> Any:
        """带默认值的输入函数
        
        Args:
            prompt: 提示信息
            default_value: 默认值
            value_type: 值类型
            
        Returns:
            用户输入的值或默认值
        """
        try:
            user_input = input(f"{prompt} (默认: {default_value}): ").strip()
            
            if not user_input:
                return default_value
            
            if value_type == int:
                return int(user_input)
            elif value_type == float:
                return float(user_input)
            elif value_type == bool:
                return user_input.lower() in ['true', 'yes', 'y', '1']
            else:
                return user_input
                
        except ValueError:
            print(f"❌ 输入格式错误，使用默认值: {default_value}")
            return default_value
        except KeyboardInterrupt:
            print(f"\n❌ 用户中断操作")
            raise KeyboardInterrupt("用户中断配置过程")
    
    def input_nft_ids(self) -> List[int]:
        """输入目标NFT ID列表
        
        Returns:
            NFT ID列表
        """
        print("\n🎯 配置目标NFT IDs:")
        print("请输入要购买的NFT ID，多个ID用逗号分隔")
        print("示例: 83,84,85 或者直接回车跳过（将使用随机策略）")
        
        try:
            user_input = input("目标NFT IDs: ").strip()
            
            if not user_input:
                print("✅ 未指定NFT ID，将使用随机购买策略")
                return []
            
            # 解析逗号分隔的ID列表
            nft_ids = []
            for id_str in user_input.split(','):
                id_str = id_str.strip()
                if id_str:
                    nft_ids.append(int(id_str))
            
            print(f"✅ 目标NFT IDs: {nft_ids}")
            return nft_ids
            
        except ValueError:
            print("❌ NFT ID格式错误，使用空列表")
            return []
        except KeyboardInterrupt:
            print("\n❌ 用户中断操作")
            raise KeyboardInterrupt("用户中断配置过程")
    
    def create_auto_buy_target(self, project_data: Dict[str, Any]) -> AutoBuyTarget:
        """创建自动购买目标

        Args:
            project_data: 选择的项目原始数据

        Returns:
            自动购买目标配置
        """
        project_name = project_data.get("project_name", "Unknown")
        print(f"\n⚙️ 配置项目 {project_name} 的购买参数:")
        
        # 输入目标NFT IDs
        target_nft_ids = self.input_nft_ids()
        
        # 输入最大购买数量
        max_quantity = self.input_with_default(
            "最大购买数量",
            len(target_nft_ids) if target_nft_ids else 1,
            int
        )
        
        # 从原始数据中获取轮次信息
        last_round = project_data.get("last_round", int(datetime.now().timestamp()))
        sec_per_round = project_data.get("sec_per_round", 3600)

        print(f"\n⏰ 轮次时间信息:")
        print(f"   上次轮次: {datetime.fromtimestamp(last_round)}")
        print(f"   轮次间隔: {sec_per_round}秒 ({sec_per_round/3600:.1f}小时)")

        # 允许用户修改轮次信息（可选）
        modify_round = self.input_with_default(
            "是否修改轮次信息",
            False,
            bool
        )

        if modify_round:
            last_round = self.input_with_default(
                "上次轮次时间戳",
                last_round,
                int
            )

            sec_per_round = self.input_with_default(
                "每轮间隔秒数",
                sec_per_round,
                int
            )

        # 获取creator信息
        creator = project_data.get("creator_pubkey", "")
        if creator:
            print(f"✅ 获取到项目creator: {creator}")
        else:
            print("⚠️ 未找到项目creator信息")

        return AutoBuyTarget(
            project_id=project_data.get("project_pubkey", ""),
            project_name=project_name,
            mint_pubkey=project_data.get("mint_pubkey", project_data.get("project_pubkey", "")),
            last_round=last_round,
            sec_per_round=sec_per_round,
            target_nft_ids=target_nft_ids,
            max_quantity=max_quantity,
            creator=creator  # 添加creator信息
        )
    
    def create_schedule_config(self) -> ScheduleConfig:
        """创建调度配置
        
        Returns:
            调度配置
        """
        print("\n📅 调度配置:")
        
        use_api_time = self.input_with_default(
            "使用API时间进行轮次等待",
            True,
            bool
        )
        
        countdown_seconds = self.input_with_default(
            "倒计时秒数",
            5,
            int
        )
        
        batch_mode = self.input_with_default(
            "批量模式",
            False,
            bool
        )
        
        batch_size = 1
        interval_ms = 10
        
        if batch_mode:
            batch_size = self.input_with_default(
                "批量大小",
                1,
                int
            )
            
            interval_ms = self.input_with_default(
                "批量间隔(毫秒)",
                100,
                int
            )
        
        return ScheduleConfig(
            use_api_time=use_api_time,
            countdown_seconds=countdown_seconds,
            batch_mode=batch_mode,
            batch_size=batch_size,
            interval_ms=interval_ms
        )
    
    def create_advanced_settings(self) -> tuple[RetrySettings, PriorityFeeSettings, ConcurrencySettings]:
        """创建高级设置
        
        Returns:
            重试设置、优先费用设置、并发设置的元组
        """
        print("\n🔧 高级设置:")
        
        # 重试设置
        print("\n🔄 重试设置:")
        max_retries = self.input_with_default("每个NFT最大重试次数", 3, int)
        retry_delay = self.input_with_default("重试延迟秒数", 0, int)
        timeout_minutes = self.input_with_default("总超时分钟数", 1, int)
        
        retry_settings = RetrySettings(
            max_retries_per_nft=max_retries,
            retry_delay_seconds=retry_delay,
            total_timeout_minutes=timeout_minutes
        )
        
        # 优先费用设置
        print("\n💰 优先费用设置:")
        low_fee = self.input_with_default("低级费用(microlamports)", 50000, int)
        medium_fee = self.input_with_default("中级费用(microlamports)", 550000, int)
        default_level = self.input_with_default("默认费用级别(low/medium)", "medium", str)
        auto_adjust = self.input_with_default("自动调整费用", True, bool)
        
        priority_fee_settings = PriorityFeeSettings(
            low_microlamports=low_fee,
            medium_microlamports=medium_fee,
            default_level=default_level,
            auto_adjust=auto_adjust
        )
        
        # 并发设置
        print("\n🔄 并发设置:")
        max_concurrent = self.input_with_default("最大并发交易数", 5, int)
        interval_ms = self.input_with_default("交易间隔(毫秒)", 10, int)
        staggered_sending = self.input_with_default("错开发送", True, bool)
        
        concurrency_settings = ConcurrencySettings(
            max_concurrent_transactions=max_concurrent,
            transaction_interval_ms=interval_ms,
            use_staggered_sending=staggered_sending
        )
        
        return retry_settings, priority_fee_settings, concurrency_settings

    async def interactive_config_creation(self) -> Optional[AutoBuyConfig]:
        """交互式配置创建

        Returns:
            创建的配置，如果取消则返回None
        """
        try:
            print("🚀 PPP.fun 自动交易配置生成器")
            print("=" * 50)

            # 获取项目列表
            print("📡 正在获取项目列表...")
            projects_data = await self.get_available_projects_with_rounds()

            if not projects_data:
                print("❌ 无法获取项目列表，请检查网络连接")
                return None

            # 选择项目
            selected_project_data = self.select_project_from_list(projects_data)
            if not selected_project_data:
                return None

            # 基本配置
            print("\n📝 基本配置:")
            project_name = selected_project_data.get("project_name", "Unknown")
            config_name = self.input_with_default(
                "配置名称",
                f"{project_name}_自动交易",
                str
            )

            config_description = self.input_with_default(
                "配置描述",
                f"自动购买 {project_name} 项目的NFT",
                str
            )

            # 创建购买目标
            target = self.create_auto_buy_target(selected_project_data)

            # 创建调度配置
            schedule = self.create_schedule_config()

            # 安全设置
            print("\n🔒 安全设置:")
            dry_run = self.input_with_default(
                "干运行模式(推荐开启)",
                True,
                bool
            )

            # 钱包私钥设置（可选）
            print("\n💰 钱包设置:")
            print("可以在配置中设置钱包私钥，或者留空使用环境变量/配置文件")
            wallet_private_key = self.input_with_default(
                "钱包私钥(可选，留空则使用其他配置)",
                "",
                str
            )

            # 如果用户输入了私钥，进行基本验证
            if wallet_private_key:
                if len(wallet_private_key) < 80:  # 基本长度检查
                    print("⚠️ 警告：私钥长度似乎不正确，请确认输入正确")
                    confirm = self.input_with_default(
                        "是否继续使用此私钥",
                        False,
                        bool
                    )
                    if not confirm:
                        wallet_private_key = ""
                        print("✅ 已清空私钥，将使用其他配置方式")
                else:
                    print("✅ 私钥已设置到配置中")
            else:
                print("✅ 未设置私钥，将使用环境变量或其他配置文件")

            # 询问是否配置高级设置
            configure_advanced = self.input_with_default(
                "配置高级设置(重试、费用、并发)",
                False,
                bool
            )

            retry_settings = None
            priority_fee_settings = None
            concurrency_settings = None

            if configure_advanced:
                retry_settings, priority_fee_settings, concurrency_settings = self.create_advanced_settings()

            # 创建配置对象
            config = AutoBuyConfig(
                name=config_name,
                description=config_description,
                targets=[target],
                schedule=schedule,
                wallet_private_key=wallet_private_key if wallet_private_key else None,
                dry_run=dry_run,
                retry_settings=retry_settings,
                priority_fee_settings=priority_fee_settings,
                concurrency_settings=concurrency_settings,
                created_at=datetime.now().isoformat()
            )

            # 验证配置
            validation_errors = config.validate()
            if validation_errors:
                print("\n❌ 配置验证失败:")
                for error in validation_errors:
                    print(f"   - {error}")
                return None

            print("\n✅ 配置创建成功!")
            return config

        except KeyboardInterrupt:
            print("\n❌ 用户中断配置创建")
            return None
        except Exception as e:
            logger.error(f"❌ 配置创建失败: {e}")
            print(f"❌ 配置创建失败: {e}")
            return None

    def generate_config_filename(self, config: AutoBuyConfig) -> str:
        """生成配置文件名

        Args:
            config: 配置对象

        Returns:
            配置文件名
        """
        # 使用项目名称和时间戳生成文件名
        primary_target = config.get_primary_target()
        project_name = primary_target.project_name if primary_target else "unknown"
        # 清理文件名中的特殊字符
        safe_name = "".join(c for c in project_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_')

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{safe_name}_{timestamp}.json"

    def save_config_to_file(self, config: AutoBuyConfig, filename: Optional[str] = None) -> str:
        """保存配置到文件

        Args:
            config: 配置对象
            filename: 文件名，如果为None则自动生成

        Returns:
            保存的文件路径
        """
        try:
            if filename is None:
                filename = self.generate_config_filename(config)

            file_path = self.config_dir / filename
            config.save_to_file(str(file_path))

            logger.info(f"✅ 配置已保存到: {file_path}")
            return str(file_path)

        except Exception as e:
            logger.error(f"❌ 保存配置失败: {e}")
            raise

    def create_template_config(self, project_mint: str, project_name: str, creator: Optional[str] = None) -> AutoBuyConfig:
        """创建配置模板

        Args:
            project_mint: 项目mint地址
            project_name: 项目名称

        Returns:
            配置模板
        """
        target = AutoBuyTarget(
            project_id=project_mint,
            project_name=project_name,
            mint_pubkey=project_mint,
            last_round=int(datetime.now().timestamp()),
            sec_per_round=3600,
            target_nft_ids=[],
            max_quantity=1,
            creator=creator
        )

        schedule = ScheduleConfig(
            use_api_time=True,
            countdown_seconds=5,
            batch_mode=False,
            batch_size=1,
            interval_ms=10
        )

        return AutoBuyConfig(
            name=f"{project_name}_模板配置",
            description=f"自动购买 {project_name} 项目NFT的模板配置",
            targets=[target],
            schedule=schedule,
            dry_run=True,
            created_at=datetime.now().isoformat()
        )

    def display_config_summary(self, config: AutoBuyConfig) -> None:
        """显示配置摘要

        Args:
            config: 配置对象
        """
        print("\n📋 配置摘要:")
        print("=" * 50)
        print(f"配置名称: {config.name}")
        print(f"配置描述: {config.description}")
        print(f"干运行模式: {config.dry_run}")
        print(f"目标数量: {len(config.targets)}")

        for i, target in enumerate(config.targets, 1):
            print(f"\n目标 {i}:")
            print(f"  项目名称: {target.project_name}")
            print(f"  项目mint: {target.mint_pubkey}")
            print(f"  目标NFT IDs: {target.target_nft_ids if target.target_nft_ids else '随机选择'}")
            print(f"  最大数量: {target.max_quantity}")
            print(f"  轮次间隔: {target.sec_per_round}秒 ({target.sec_per_round/3600:.1f}小时)")

        print(f"\n调度配置:")
        print(f"  使用API时间: {config.schedule.use_api_time}")
        print(f"  倒计时: {config.schedule.countdown_seconds}秒")
        print(f"  批量模式: {config.schedule.batch_mode}")

        if config.retry_settings:
            print(f"\n重试设置:")
            print(f"  最大重试: {config.retry_settings.max_retries_per_nft}次")
            print(f"  重试延迟: {config.retry_settings.retry_delay_seconds}秒")

        if config.priority_fee_settings:
            print(f"\n优先费用:")
            print(f"  默认级别: {config.priority_fee_settings.default_level}")
            print(f"  低级费用: {config.priority_fee_settings.low_microlamports} microlamports")
            print(f"  中级费用: {config.priority_fee_settings.medium_microlamports} microlamports")

        if config.concurrency_settings:
            print(f"\n并发设置:")
            print(f"  最大并发: {config.concurrency_settings.max_concurrent_transactions}")
            print(f"  交易间隔: {config.concurrency_settings.transaction_interval_ms}ms")

        print("=" * 50)
