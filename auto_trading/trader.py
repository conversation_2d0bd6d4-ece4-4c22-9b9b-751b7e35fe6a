"""
PPP.fun 并发交易执行器
实现并发控制、错开发送和优先费用管理
"""

import asyncio
import json
import random
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

# 日志配置
import sys
sys.path.append('..')
from log_config import setup_logging, get_logger

# 初始化日志（使用全局项目名称）
setup_logging(app_name="trader")
logger = get_logger()

from .models import AutoBuyTarget, PriorityFeeSettings, ConcurrencySettings, RetrySettings
from ppp_simple_buyer import PPPSimpleBuyer
from ppp_api import PPPAPIClient


class PriorityFeeManager:
    """优先费用管理器"""
    
    def __init__(self, settings: PriorityFeeSettings):
        """初始化优先费用管理器
        
        Args:
            settings: 优先费用设置
        """
        self.settings = settings
        logger.info(f"💰 优先费用管理器初始化: {settings.default_level}级别")
        logger.info(f"   低级费用: {settings.low_microlamports} microlamports")
        logger.info(f"   中级费用: {settings.medium_microlamports} microlamports")
    
    def get_priority_fee(self, level: Optional[str] = None) -> int:
        """获取优先费用
        
        Args:
            level: 费用级别 ("low" 或 "medium")，None使用默认级别
            
        Returns:
            优先费用（microlamports）
        """
        if level is None:
            level = self.settings.default_level
        
        if level == "low":
            return self.settings.low_microlamports
        elif level == "medium":
            return self.settings.medium_microlamports
        else:
            logger.warning(f"⚠️ 未知的费用级别: {level}，使用默认级别")
            return self.get_priority_fee(self.settings.default_level)
    
    def auto_adjust_fee(self, network_congestion: float = 0.5) -> int:
        """根据网络拥堵情况自动调整费用
        
        Args:
            network_congestion: 网络拥堵程度 (0.0-1.0)
            
        Returns:
            调整后的优先费用
        """
        if not self.settings.auto_adjust:
            return self.get_priority_fee()
        
        # 简单的自动调整逻辑
        if network_congestion > 0.7:
            level = "medium"
        else:
            level = "low"
        
        fee = self.get_priority_fee(level)
        logger.debug(f"🔧 自动调整费用: 拥堵度{network_congestion:.1f} → {level}级别 ({fee} microlamports)")
        return fee


class ConcurrentTrader:
    """并发交易执行器"""
    
    def __init__(self, buyer: PPPSimpleBuyer, api_client: PPPAPIClient):
        """初始化并发交易执行器

        Args:
            buyer: PPPSimpleBuyer实例，复用现有购买逻辑
            api_client: PPPAPIClient实例，用于获取NFT数据
        """
        self.buyer = buyer
        self.api_client = api_client
        self._semaphore = None  # 将在设置并发数时初始化
        self.fee_manager = None
        self._stop_event = asyncio.Event()

        # NFT池管理（用于并发安全的随机分配）
        self._available_nft_pool = []
        self._pool_lock = asyncio.Lock()
        self._allocated_nfts = set()  # 已分配的NFT ID集合

        logger.info("🔄 并发交易执行器初始化完成")
    
    def configure(self, concurrency_settings: ConcurrencySettings, 
                  priority_fee_settings: PriorityFeeSettings,
                  retry_settings: RetrySettings):
        """配置并发交易执行器
        
        Args:
            concurrency_settings: 并发设置
            priority_fee_settings: 优先费用设置
            retry_settings: 重试设置
        """
        # 设置并发控制
        self._semaphore = asyncio.Semaphore(concurrency_settings.max_concurrent_transactions)
        self.concurrency_settings = concurrency_settings
        self.retry_settings = retry_settings
        
        # 设置优先费用管理器
        self.fee_manager = PriorityFeeManager(priority_fee_settings)
        
        logger.info(f"⚙️ 并发交易执行器配置完成:")
        logger.info(f"   最大并发数: {concurrency_settings.max_concurrent_transactions}")
        logger.info(f"   交易间隔: {concurrency_settings.transaction_interval_ms}ms")
        logger.info(f"   错开发送: {concurrency_settings.use_staggered_sending}")
        logger.info(f"   最大重试: {retry_settings.max_retries_per_nft}")
    
    async def purchase_with_retry(self, nft_id: int, dry_run: bool = True) -> Dict[str, Any]:
        """带重试的购买方法
        
        Args:
            nft_id: NFT ID
            dry_run: 是否为干运行模式
            
        Returns:
            购买结果
        """
        last_error = None
        
        for attempt in range(self.retry_settings.max_retries_per_nft + 1):
            try:
                if self._stop_event.is_set():
                    return {
                        "success": False,
                        "nft_id": nft_id,
                        "error_message": "购买被中断",
                        "attempt": attempt
                    }
                
                if attempt > 0:
                    logger.info(f"🔄 NFT #{nft_id} 第{attempt}次重试")
                    
                    # 重试延迟
                    if self.retry_settings.retry_delay_seconds > 0:
                        await asyncio.sleep(self.retry_settings.retry_delay_seconds)
                
                # 调用PPPSimpleBuyer的buy_nft方法，使用字典参数格式
                # 从当前正在处理的目标获取mint地址和creator
                project_mint = getattr(self, '_current_target_mint', None)
                creator = getattr(self, '_current_target_creator', None)

                if not creator:
                    raise ValueError("creator参数是必需的，请确保目标配置中包含creator信息")

                buy_params = {
                    "nft_id": nft_id,
                    "creator": creator,
                    "dry_run": dry_run,
                    "project_mint": project_mint
                }
                result = await self.buyer.buy_nft(buy_params)
                
                # 添加尝试次数信息
                result["attempt"] = attempt + 1
                
                if result.get("success", False):
                    if attempt > 0:
                        logger.info(f"✅ NFT #{nft_id} 重试成功 (第{attempt + 1}次尝试)")
                    return result
                else:
                    last_error = result.get("error_message", "未知错误")
                    logger.warning(f"⚠️ NFT #{nft_id} 第{attempt + 1}次尝试失败: {last_error}")
                    
            except Exception as e:
                last_error = str(e)
                logger.error(f"❌ NFT #{nft_id} 第{attempt + 1}次尝试异常: {e}")
        
        # 所有重试都失败
        return {
            "success": False,
            "nft_id": nft_id,
            "error_message": f"重试{self.retry_settings.max_retries_per_nft}次后仍失败: {last_error}",
            "attempt": self.retry_settings.max_retries_per_nft + 1
        }
    
    async def _execute_single_purchase(self, nft_id: int, delay_ms: int, dry_run: bool) -> Dict[str, Any]:
        """执行单个购买任务（带并发控制）
        
        Args:
            nft_id: NFT ID
            delay_ms: 延迟毫秒数
            dry_run: 是否为干运行模式
            
        Returns:
            购买结果
        """
        async with self._semaphore:
            try:
                # 错开发送延迟
                if delay_ms > 0:
                    await asyncio.sleep(delay_ms / 1000.0)
                
                start_time = datetime.now()
                result = await self.purchase_with_retry(nft_id, dry_run)
                end_time = datetime.now()
                
                # 添加执行时间信息
                execution_time = (end_time - start_time).total_seconds()
                result["execution_time"] = execution_time
                result["start_time"] = start_time.isoformat()
                result["end_time"] = end_time.isoformat()
                
                return result
                
            except Exception as e:
                logger.error(f"❌ NFT #{nft_id} 购买任务异常: {e}")
                return {
                    "success": False,
                    "nft_id": nft_id,
                    "error_message": f"购买任务异常: {e}",
                    "execution_time": 0
                }

    async def _execute_single_purchase_from_pool(self, target: AutoBuyTarget, delay_ms: int, dry_run: bool) -> Dict[str, Any]:
        """从NFT池中动态分配并执行单个购买任务

        Args:
            target: 购买目标
            delay_ms: 延迟毫秒数
            dry_run: 是否为干运行模式

        Returns:
            购买结果
        """
        async with self._semaphore:
            try:
                # 错开发送延迟
                if delay_ms > 0:
                    await asyncio.sleep(delay_ms / 1000.0)

                # 从池中分配NFT
                nft_id = await self._allocate_nft_from_pool()
                if nft_id is None:
                    return {
                        "success": False,
                        "nft_id": None,
                        "error_message": "NFT池已空，无法分配NFT",
                        "execution_time": 0
                    }

                start_time = datetime.now()
                result = await self.purchase_with_retry(nft_id, dry_run)
                end_time = datetime.now()

                # 如果购买失败，将NFT返回池中
                if not result.get("success", False):
                    await self._return_nft_to_pool(nft_id)

                # 添加执行时间信息
                execution_time = (end_time - start_time).total_seconds()
                result["execution_time"] = execution_time
                result["start_time"] = start_time.isoformat()
                result["end_time"] = end_time.isoformat()

                return result

            except Exception as e:
                logger.error(f"❌ 池模式购买任务异常: {e}")
                return {
                    "success": False,
                    "nft_id": None,
                    "error_message": f"购买任务异常: {e}",
                    "execution_time": 0
                }
    
    async def execute_concurrent_purchases_with_pool(self, target: AutoBuyTarget, dry_run: bool = True) -> Dict[str, Any]:
        """使用NFT池执行并发购买（推荐用于随机购买）

        Args:
            target: 购买目标
            dry_run: 是否为干运行模式

        Returns:
            并发购买结果统计
        """
        if not self._semaphore:
            raise RuntimeError("并发交易执行器未配置，请先调用configure()方法")

        # 初始化NFT池
        pool_initialized = await self._initialize_nft_pool(target)
        if not pool_initialized:
            return {
                "total_nfts": 0,
                "successful_purchases": 0,
                "failed_purchases": 0,
                "purchase_details": [],
                "errors": ["NFT池初始化失败"],
                "start_time": datetime.now().isoformat()
            }

        results = {
            "total_nfts": min(target.max_quantity, len(self._available_nft_pool)),
            "successful_purchases": 0,
            "failed_purchases": 0,
            "purchase_details": [],
            "errors": [],
            "start_time": datetime.now().isoformat(),
            "concurrent_settings": {
                "max_concurrent": self.concurrency_settings.max_concurrent_transactions,
                "interval_ms": self.concurrency_settings.transaction_interval_ms,
                "staggered_sending": self.concurrency_settings.use_staggered_sending
            }
        }

        try:
            # 设置当前目标的mint地址和creator，供购买方法使用
            self._current_target_mint = target.mint_pubkey
            self._current_target_creator = target.creator

            if not target.creator:
                raise ValueError(f"目标 {target.project_name} 缺少creator信息，请更新配置文件")

            logger.info(f"🚀 开始并发购买，目标数量: {target.max_quantity}")
            logger.info(f"   并发数: {self.concurrency_settings.max_concurrent_transactions}")
            logger.info(f"   错开间隔: {self.concurrency_settings.transaction_interval_ms}ms")

            # 重置停止事件
            self._stop_event.clear()

            # 创建购买任务（动态分配NFT）
            tasks = []
            for i in range(min(target.max_quantity, len(self._available_nft_pool))):
                # 计算错开发送的延迟
                delay_ms = 0
                if self.concurrency_settings.use_staggered_sending:
                    delay_ms = i * self.concurrency_settings.transaction_interval_ms

                task = self._execute_single_purchase_from_pool(target, delay_ms, dry_run)
                tasks.append(task)

            # 并发执行所有购买任务
            purchase_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            for result in purchase_results:
                if isinstance(result, Exception):
                    logger.error(f"❌ 购买任务异常: {result}")
                    results["errors"].append(f"购买任务异常: {result}")
                    results["failed_purchases"] += 1
                else:
                    results["purchase_details"].append(result)
                    if result.get("success", False):
                        results["successful_purchases"] += 1
                    else:
                        results["failed_purchases"] += 1

            results["end_time"] = datetime.now().isoformat()

            logger.info(f"✅ 并发购买完成")
            logger.info(f"   成功: {results['successful_purchases']}")
            logger.info(f"   失败: {results['failed_purchases']}")

            return results

        except Exception as e:
            logger.error(f"❌ 并发购买执行失败: {e}")
            results["errors"].append(f"并发购买执行失败: {e}")
            results["end_time"] = datetime.now().isoformat()
            return results

    async def execute_concurrent_purchases(self, nft_ids: List[int], dry_run: bool = True) -> Dict[str, Any]:
        """执行并发购买
        
        Args:
            nft_ids: 要购买的NFT ID列表
            dry_run: 是否为干运行模式
            
        Returns:
            并发购买结果统计
        """
        if not self._semaphore:
            raise RuntimeError("并发交易执行器未配置，请先调用configure()方法")
        
        results = {
            "total_nfts": len(nft_ids),
            "successful_purchases": 0,
            "failed_purchases": 0,
            "purchase_details": [],
            "errors": [],
            "start_time": datetime.now().isoformat(),
            "concurrent_settings": {
                "max_concurrent": self.concurrency_settings.max_concurrent_transactions,
                "interval_ms": self.concurrency_settings.transaction_interval_ms,
                "staggered_sending": self.concurrency_settings.use_staggered_sending
            }
        }
        
        try:
            logger.info(f"🚀 开始并发购买 {len(nft_ids)} 个NFT")
            logger.info(f"   并发数: {self.concurrency_settings.max_concurrent_transactions}")
            logger.info(f"   错开间隔: {self.concurrency_settings.transaction_interval_ms}ms")
            
            # 重置停止事件
            self._stop_event.clear()
            
            # 创建购买任务
            tasks = []
            for i, nft_id in enumerate(nft_ids):
                # 计算错开发送的延迟
                delay_ms = 0
                if self.concurrency_settings.use_staggered_sending:
                    delay_ms = i * self.concurrency_settings.transaction_interval_ms
                
                task = self._execute_single_purchase(nft_id, delay_ms, dry_run)
                tasks.append(task)
            
            # 并发执行所有购买任务
            purchase_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for i, result in enumerate(purchase_results):
                nft_id = nft_ids[i]
                
                if isinstance(result, Exception):
                    # 处理异常
                    error_msg = f"NFT #{nft_id} 任务异常: {result}"
                    logger.error(f"❌ {error_msg}")
                    
                    results["failed_purchases"] += 1
                    results["errors"].append(error_msg)
                    results["purchase_details"].append({
                        "success": False,
                        "nft_id": nft_id,
                        "error_message": str(result)
                    })
                else:
                    # 处理正常结果
                    results["purchase_details"].append(result)
                    
                    if result.get("success", False):
                        results["successful_purchases"] += 1
                        logger.info(f"✅ NFT #{nft_id} 购买成功")
                    else:
                        results["failed_purchases"] += 1
                        error_msg = result.get("error_message", "未知错误")
                        results["errors"].append(f"NFT #{nft_id}: {error_msg}")
                        logger.error(f"❌ NFT #{nft_id} 购买失败: {error_msg}")
            
            results["end_time"] = datetime.now().isoformat()
            
            # 输出统计结果
            logger.info(f"📊 并发购买完成:")
            logger.info(f"   总数: {results['total_nfts']}")
            logger.info(f"   成功: {results['successful_purchases']}")
            logger.info(f"   失败: {results['failed_purchases']}")
            logger.info(f"   成功率: {results['successful_purchases']/results['total_nfts']*100:.1f}%")
            
            return results
            
        except Exception as e:
            error_msg = f"并发购买执行失败: {e}"
            logger.error(f"❌ {error_msg}")
            results["errors"].append(error_msg)
            results["end_time"] = datetime.now().isoformat()
            return results

    async def update_local_nft_cache(self, target: AutoBuyTarget) -> bool:
        """更新本地NFT缓存

        Args:
            target: 购买目标

        Returns:
            是否更新成功
        """
        try:
            logger.info(f"🔄 更新项目 {target.project_name} 的本地NFT缓存")

            # 从API获取最新NFT数据
            nfts = await self.api_client.get_project_nfts(target.mint_pubkey, limit=500)

            if not nfts:
                logger.warning(f"⚠️ 项目 {target.project_name} 没有NFT数据")
                return False

            # 准备缓存数据格式（与现有格式兼容）
            cache_data = {
                "project_mint": target.mint_pubkey,
                "project_name": target.project_name,
                "updated_at": datetime.now().isoformat(),
                "data": []
            }

            # 转换NFT数据格式（与PPPSimpleBuyer期望的格式一致）
            for nft in nfts:
                try:
                    # 安全地获取属性，处理可能的None值
                    attributes = getattr(nft, 'attributes', {}) or {}

                    # 安全的价格转换
                    price_lamports = 0
                    if hasattr(nft, 'price') and nft.price is not None:
                        try:
                            price_lamports = int(float(nft.price) * 10**9)
                        except (ValueError, TypeError):
                            logger.warning(f"⚠️ NFT #{nft.id} 价格转换失败: {nft.price}")
                            price_lamports = 0

                    nft_data = {
                        "nft_id": getattr(nft, 'id', 0),
                        "name": getattr(nft, 'name', ''),
                        "image": getattr(nft, 'image', ''),
                        "price": price_lamports,
                        "owner": getattr(nft, 'owner', ''),
                        "nft_pubkey": getattr(nft, 'mint_address', ''),  # NFT的mint地址
                        "mint_pubkey": target.mint_pubkey,  # 项目的mint地址
                        "owner_pubkey": getattr(nft, 'owner', ''),
                        "rarity_rank": getattr(nft, 'rarity_rank', 0),
                        "round": attributes.get("round", 0),
                        "split_count": attributes.get("split_count", 1),
                        "last_trade": attributes.get("last_trade", 0),
                        "create_time": attributes.get("create_time", ""),
                        "bump": attributes.get("bump", 255),
                    }
                    cache_data["data"].append(nft_data)

                except Exception as e:
                    logger.warning(f"⚠️ 处理NFT数据时出错: {e}, NFT: {getattr(nft, 'id', 'unknown')}")
                    continue

            # 确保目录存在
            cache_dir = Path("api_result")
            cache_dir.mkdir(exist_ok=True)

            # 使用项目mint地址作为文件名，确保每个项目独立缓存
            cache_filename = f"nfts_{target.mint_pubkey}.json"
            cache_file = cache_dir / cache_filename
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)

            logger.info(f"✅ 成功更新本地NFT缓存: {len(nfts)} 个NFT")
            return True

        except Exception as e:
            logger.error(f"❌ 更新本地NFT缓存失败: {e}")
            return False

    def _load_nfts_from_cache(self, mint_pubkey: str) -> List:
        """从缓存文件加载NFT数据

        Args:
            mint_pubkey: 项目的mint公钥

        Returns:
            NFT对象列表，如果缓存不存在或读取失败则返回空列表
        """
        cache_file = Path("api_result") / f"nfts_{mint_pubkey}.json"
        if not cache_file.exists():
            logger.info(f"🔍 缓存文件不存在: {cache_file}")
            return []

        try:
            # 从缓存文件读取NFT
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            # 转换为API格式的NFT对象
            from types import SimpleNamespace
            cached_nfts = []
            for nft_data in cache_data.get("data", []):
                nft_obj = SimpleNamespace()
                nft_obj.id = nft_data.get("nft_id", 0)
                nft_obj.name = nft_data.get("name", "")
                nft_obj.image = nft_data.get("image", "")
                nft_obj.price = nft_data.get("price", 0) / 10**9  # 转换回SOL

                nft_obj.owner = nft_data.get("owner", "")
                nft_obj.mint_address = nft_data.get("nft_pubkey", "")
                nft_obj.rarity_rank = nft_data.get("rarity_rank", 0)
                # 添加attributes
                nft_obj.attributes = {
                    "round": nft_data.get("round", 0),
                    "split_count": nft_data.get("split_count", 1),
                    "last_trade": nft_data.get("last_trade", 0),
                    "create_time": nft_data.get("create_time", ""),
                    "bump": nft_data.get("bump", 255)
                }
                cached_nfts.append(nft_obj)

            logger.info(f"✅ 从项目缓存找到 {len(cached_nfts)} 个NFT")
            return cached_nfts

        except Exception as e:
            logger.warning(f"⚠️ 读取缓存文件失败: {e}")
            return []

    async def get_available_nfts(self, target: AutoBuyTarget, strategy: str = "specific", use_pool: bool = True) -> List[int]:
        """获取可用的NFT列表

        Args:
            target: 购买目标
            strategy: 购买策略 ("specific", "random", "mixed")
            use_pool: 是否使用NFT池进行动态分配（用于并发购买）

        Returns:
            NFT ID列表
        """
        try:
            # 如果使用池模式，返回单个NFT用于动态分配
            if use_pool and strategy == "random":
                nft_id = await self._allocate_nft_from_pool()
                if nft_id is not None:
                    return [nft_id]
                else:
                    logger.warning(f"⚠️ NFT池已空，无法分配更多NFT")
                    return []

            if strategy == "specific" and target.target_nft_ids:
                # 指定ID策略
                nft_ids = target.target_nft_ids[:target.max_quantity]
                logger.info(f"📋 使用指定ID策略: {nft_ids}")
                return nft_ids
            
            else:
                logger.error(f"❌ 未知的购买策略: {strategy}")
                return []
                
        except Exception as e:
            logger.error(f"❌ 获取可用NFT失败: {e}")
            return []

    async def _filter_purchasable_nfts(self, nfts: List, target: AutoBuyTarget) -> List[int]:
        """筛选真正可购买的NFT

        Args:
            nfts: NFT列表
            target: 购买目标

        Returns:
            可购买的NFT ID列表
        """
        purchasable_ids = []

        # 获取项目的轮次信息
        try:
            from ppp_api import ProjectDataManager
            project_manager = ProjectDataManager(self.api_client)
            project_info = await project_manager.get_project_with_cache(target.mint_pubkey)

            if not project_info:
                logger.warning(f"⚠️ 无法获取项目轮次信息，使用简单筛选")
                # 如果无法获取项目信息，只做基本筛选
                return [nft.id for nft in nfts if nft.price > 0]

            current_round_start = project_info.last_round
            logger.debug(f"当前轮次开始时间: {datetime.fromtimestamp(current_round_start)}")

        except Exception as e:
            logger.error(f"❌ 获取项目轮次信息失败: {e}")
            return [nft.id for nft in nfts if nft.price > 0]

        for nft in nfts:
            try:
                # 安全地获取NFT属性
                nft_id = getattr(nft, 'id', 0)
                nft_price = getattr(nft, 'price', 0)

                # 基本检查：价格合理
                if nft_price <= 0:
                    logger.debug(f"跳过NFT #{nft_id}: 价格无效 ({nft_price})")
                    continue

                # 关键检查：NFT是否在当前轮次中被交易过
                attributes = getattr(nft, 'attributes', {}) or {}
                last_trade = attributes.get("last_trade", 0)

                if last_trade > 0 and last_trade >= current_round_start:
                    logger.debug(f"跳过NFT #{nft_id}: 在当前轮次中已被交易 (last_trade: {last_trade})")
                    continue

                purchasable_ids.append(nft_id)

            except Exception as e:
                logger.warning(f"⚠️ 筛选NFT时出错: {e}, NFT: {getattr(nft, 'id', 'unknown')}")
                continue

        logger.info(f"🔍 从 {len(nfts)} 个上架NFT中筛选出 {len(purchasable_ids)} 个可购买NFT")
        return purchasable_ids

    async def execute_rapid_purchases(self, nft_ids: List[int], dry_run: bool = True, target: AutoBuyTarget = None) -> Dict[str, Any]:
        """执行高速抢购（无间隔，异步发送+异步处理）

        Args:
            nft_ids: 要购买的NFT ID列表
            dry_run: 是否为干运行模式

        Returns:
            购买结果统计
        """
        results = {
            "total_nfts": len(nft_ids),
            "successful_purchases": 0,
            "failed_purchases": 0,
            "purchase_details": [],
            "errors": [],
            "start_time": datetime.now().isoformat(),
            "mode": "rapid_purchase"
        }

        try:
            logger.info(f"⚡ 开始高速抢购 {len(nft_ids)} 个NFT（无间隔模式）")

            # 创建发送队列和结果队列
            send_queue = asyncio.Queue()
            result_queue = asyncio.Queue()

            # 将所有NFT ID放入发送队列
            for nft_id in nft_ids:
                await send_queue.put(nft_id)

            # 启动发送线程（快速串行发送）
            sender_task = asyncio.create_task(
                self._rapid_sender_worker(send_queue, result_queue, dry_run, target)
            )

            # 启动结果处理线程
            processor_task = asyncio.create_task(
                self._result_processor_worker(result_queue, results, len(nft_ids))
            )

            # 等待发送完成
            await sender_task

            # 等待结果处理完成
            await processor_task

            results["end_time"] = datetime.now().isoformat()

            logger.info(f"⚡ 高速抢购完成: 成功{results['successful_purchases']}, 失败{results['failed_purchases']}")

        except Exception as e:
            logger.error(f"❌ 高速抢购执行失败: {e}")
            results["errors"].append(f"高速抢购执行失败: {e}")
            results["end_time"] = datetime.now().isoformat()

        return results

    async def _rapid_sender_worker(self, send_queue: asyncio.Queue, result_queue: asyncio.Queue, dry_run: bool, target: AutoBuyTarget = None):
        """快速发送工作线程（无间隔串行发送）"""
        try:
            while not send_queue.empty():
                nft_id = await send_queue.get()

                # 立即发送交易，不等待结果
                asyncio.create_task(self._send_transaction_async(nft_id, result_queue, dry_run, target))

                # 不等待task完成，立即处理下一个
                send_queue.task_done()

        except Exception as e:
            logger.error(f"❌ 发送工作线程异常: {e}")

    async def _send_transaction_async(self, nft_id: int, result_queue: asyncio.Queue, dry_run: bool, target: AutoBuyTarget = None):
        """异步发送单个交易"""
        start_time = datetime.now()

        try:
            # 调用PPPSimpleBuyer购买NFT，使用字典参数格式
            if not target or not target.creator:
                raise ValueError("target和creator参数是必需的")

            buy_params = {
                "nft_id": nft_id,
                "creator": target.creator,
                "dry_run": dry_run,
                "project_mint": target.mint_pubkey
            }
            purchase_result = await self.buyer.buy_nft(buy_params)

            # 将结果放入结果队列
            result_data = {
                "nft_id": nft_id,
                "success": purchase_result.get("success", False),
                "result": purchase_result,
                "execution_time": (datetime.now() - start_time).total_seconds()
            }

            await result_queue.put(result_data)

        except Exception as e:
            # 发送失败也要记录结果
            result_data = {
                "nft_id": nft_id,
                "success": False,
                "result": {"error_message": str(e)},
                "execution_time": (datetime.now() - start_time).total_seconds()
            }

            await result_queue.put(result_data)

    async def _result_processor_worker(self, result_queue: asyncio.Queue, results: Dict[str, Any], total_count: int):
        """结果处理工作线程"""
        processed_count = 0

        try:
            while processed_count < total_count:
                # 等待结果
                result_data = await result_queue.get()

                # 处理结果
                if result_data["success"]:
                    results["successful_purchases"] += 1
                    logger.info(f"✅ NFT #{result_data['nft_id']} 购买成功 ({result_data['execution_time']:.2f}s)")
                else:
                    results["failed_purchases"] += 1
                    error_msg = result_data["result"].get("error_message", "未知错误")
                    logger.info(f"❌ NFT #{result_data['nft_id']} 购买失败: {error_msg}")

                # 记录详细结果
                results["purchase_details"].append({
                    "nft_id": result_data["nft_id"],
                    "success": result_data["success"],
                    "execution_time": result_data["execution_time"],
                    "result": result_data["result"]
                })

                processed_count += 1
                result_queue.task_done()

        except Exception as e:
            logger.error(f"❌ 结果处理工作线程异常: {e}")

    def stop(self):
        """停止并发交易"""
        logger.info("🛑 请求停止并发交易")
        self._stop_event.set()
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return not self._stop_event.is_set()

    async def _initialize_nft_pool(self, target: AutoBuyTarget) -> bool:
        """初始化NFT池

        Args:
            target: 购买目标

        Returns:
            是否成功初始化
        """
        async with self._pool_lock:
            try:
                logger.info(f"🔄 初始化项目 {target.project_name} 的NFT池")

                # 先更新本地NFT缓存
                cache_updated = await self.update_local_nft_cache(target)
                if not cache_updated:
                    logger.error(f"❌ 无法更新本地NFT缓存，终止初始化")
                    return False
                
                # 从缓存中获取NFT数据
                nfts = self._load_nfts_from_cache(target.mint_pubkey)

                # 筛选真正可购买的NFT
                available_ids = await self._filter_purchasable_nfts(nfts, target)

                # 初始化NFT池
                self._available_nft_pool = available_ids.copy()
                self._allocated_nfts.clear()

                logger.info(f"✅ NFT池初始化完成，可用NFT数量: {len(self._available_nft_pool)}")
                return len(self._available_nft_pool) > 0

            except Exception as e:
                logger.error(f"❌ 初始化NFT池失败: {e}")
                return False

    async def _allocate_nft_from_pool(self) -> Optional[int]:
        """从NFT池中分配一个NFT

        Returns:
            分配的NFT ID，如果池为空则返回None
        """
        async with self._pool_lock:
            if not self._available_nft_pool:
                return None

            # 随机选择一个NFT
            nft_id = random.choice(self._available_nft_pool)

            # 从池中移除并标记为已分配
            self._available_nft_pool.remove(nft_id)
            self._allocated_nfts.add(nft_id)

            logger.debug(f"🎯 从池中分配NFT #{nft_id}，剩余: {len(self._available_nft_pool)}")
            return nft_id

    async def _return_nft_to_pool(self, nft_id: int):
        """将NFT返回到池中（购买失败时）

        Args:
            nft_id: 要返回的NFT ID
        """
        async with self._pool_lock:
            if nft_id in self._allocated_nfts:
                self._allocated_nfts.remove(nft_id)
                self._available_nft_pool.append(nft_id)
                logger.debug(f"🔄 NFT #{nft_id} 返回到池中")

    async def debug_nft_data(self, target: AutoBuyTarget, limit: int = 10):
        """调试NFT数据，用于排查随机购买问题

        Args:
            target: 购买目标
            limit: 显示的NFT数量限制
        """
        try:
            logger.info(f"🔍 调试项目 {target.project_name} 的NFT数据")

            # 获取NFT数据
            nfts = await self.api_client.get_project_nfts(target.mint_pubkey, limit=100)

            if not nfts:
                logger.warning(f"⚠️ 项目 {target.project_name} 没有NFT数据")
                return

            logger.info(f"📊 总共获取到 {len(nfts)} 个上架NFT")

            # 显示前几个NFT的详细信息
            for i, nft in enumerate(nfts[:limit]):
                attributes = getattr(nft, 'attributes', {}) or {}
                logger.info(f"NFT #{i+1}:")
                logger.info(f"  ID: {getattr(nft, 'id', 'N/A')}")
                logger.info(f"  名称: {getattr(nft, 'name', 'N/A')}")
                logger.info(f"  价格: {getattr(nft, 'price', 'N/A')}")

                logger.info(f"  拥有者: {getattr(nft, 'owner', 'N/A')}")
                logger.info(f"  最后交易: {attributes.get('last_trade', 'N/A')}")
                logger.info(f"  轮次: {attributes.get('round', 'N/A')}")

            # 筛选可购买的NFT
            available_ids = await self._filter_purchasable_nfts(nfts, target)
            logger.info(f"🎯 可购买的NFT IDs: {available_ids}")

        except Exception as e:
            logger.error(f"❌ 调试NFT数据失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def forceLoadAvaiable(self, mint_pubkey: str, pubkey: str):
        nfts = self._load_nfts_from_cache(mint_pubkey)
        self._available_nft_pool = [nft.id for nft in nfts if nft.owner != pubkey and nft.price > 0]