"""
PPP.fun 自动交易调度器
实现时间调度、轮次等待和自动交易启动功能
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import random
import time

# 日志配置
import sys
sys.path.append('..')
from log_config import setup_logging, get_logger

# 初始化日志（使用全局项目名称）
setup_logging(app_name="scheduler")
logger = get_logger()

from .models import AutoBuyConfig, AutoBuyTarget
from ppp_api import ProjectDataManager
from ppp_simple_buyer import PPPSimpleBuyer


class TradingScheduler:
    """自动交易调度器"""
    
    def __init__(self, buyer: PPPSimpleBuyer, project_manager: ProjectDataManager, trader=None):
        """初始化调度器

        Args:
            buyer: PPPSimpleBuyer实例，复用现有购买逻辑
            project_manager: ProjectDataManager实例，用于项目信息管理
            trader: ConcurrentTrader实例，用于随机购买功能（可选）
        """
        self.buyer = buyer
        self.project_manager = project_manager
        self.trader = trader
        self._stop_event = asyncio.Event()
        logger.info("📅 自动交易调度器初始化完成")
    
    def forceLoadAvaiable(self, target: AutoBuyTarget):
        """强制刷新为可购买
        
        Keyword arguments:
        target: 购买目标
        """
        if self.trader:
            self.trader.forceLoadAvaiable(target.mint_pubkey, self.buyer.wallet_keypair.pubkey())
        


    async def _refresh_nft_cache(self, target: AutoBuyTarget):
        """刷新NFT缓存

        Args:
            target: 购买目标
        """
        try:
            if self.trader:
                logger.info(f"🔄 刷新 {target.project_name} 的NFT缓存")
                await self.trader.update_local_nft_cache(target)
                logger.info(f"✅ NFT缓存刷新完成")
            else:
                logger.warning(f"⚠️ 未配置ConcurrentTrader，跳过NFT缓存刷新")
        except Exception as e:
            logger.error(f"❌ 刷新NFT缓存失败: {e}")

    async def execute_target_purchases(self, target: AutoBuyTarget, dry_run: bool = True) -> Dict[str, Any]:
        """执行目标的购买任务
        
        Args:
            target: 购买目标配置
            dry_run: 是否为干运行模式
            
        Returns:
            购买结果统计
        """
        results = {
            "target_name": target.project_name,
            "total_attempts": 0,
            "successful_purchases": 0,
            "failed_purchases": 0,
            "purchase_details": [],
            "errors": []
        }
        
        try:
            logger.info(f"🎯 开始执行 {target.project_name} 的购买任务")
            
            # 确定要购买的NFT列表
            nft_ids_to_buy = []
            
            if target.target_nft_ids:
                # 购买指定的NFT ID，使用高速购买模式
                nft_ids_to_buy = random.sample(target.target_nft_ids, k=min(target.max_quantity,len(target.target_nft_ids)))
                logger.info(f"📋 指定购买NFT IDs: {nft_ids_to_buy}")

                if self.trader:
                    logger.info(f"⚡ 使用高速抢购模式购买指定NFT")
                    rapid_result = await self.trader.execute_rapid_purchases(nft_ids_to_buy, dry_run, target)

                    # 转换结果格式
                    results["total_attempts"] = rapid_result.get("total_nfts", 0)
                    results["successful_purchases"] = rapid_result.get("successful_purchases", 0)
                    results["failed_purchases"] = rapid_result.get("failed_purchases", 0)
                    results["errors"].extend(rapid_result.get("errors", []))
                    results["purchase_details"].extend(rapid_result.get("purchase_details", []))

                    return results
                else:
                    # 如果没有trader，使用传统方式
                    pass
            else:
                # 如果没有指定NFT ID，使用随机购买模式
                if self.trader:
                    logger.info(f"🎲 未指定NFT ID，使用随机购买模式")

                    # 使用高速抢购模式
                    logger.info(f"⚡ 使用高速抢购模式")

                    # 获取可用NFT列表
                    available_nfts = self.trader._available_nft_pool
                    if available_nfts:
                        # 限制购买数量
                        num = target.max_quantity 
                        if  num > len(available_nfts):
                            num = len(available_nfts)
                        nft_ids_to_buy = random.sample(available_nfts, k = num)
                        logger.info(f"🎯 高速抢购目标: {nft_ids_to_buy}")

                        # 使用高速购买方法
                        rapid_result = await self.trader.execute_rapid_purchases(nft_ids_to_buy, dry_run, target)

                        # 将高速购买结果转换为标准格式
                        results["total_attempts"] = rapid_result.get("total_nfts", 0)
                        results["successful_purchases"] = rapid_result.get("successful_purchases", 0)
                        results["failed_purchases"] = rapid_result.get("failed_purchases", 0)
                        results["errors"].extend(rapid_result.get("errors", []))
                        results["purchase_details"].extend(rapid_result.get("purchase_details", []))

                        return results
                    else:
                        # 使用串行随机购买
                        logger.info(f"🔄 使用串行随机购买模式")
                        nft_ids_to_buy = await self.trader.get_available_nfts(target, "random")
                        if nft_ids_to_buy:
                            logger.info(f"🎲 随机选择NFT IDs: {nft_ids_to_buy}")
                        else:
                            logger.warning(f"⚠️ 未找到可用的NFT进行随机购买")
                            results["errors"].append("未找到可用的NFT进行随机购买")
                            return results
                else:
                    logger.warning(f"⚠️ 未指定NFT ID且未配置ConcurrentTrader，无法进行随机购买")
                    results["errors"].append("未指定NFT ID且未配置ConcurrentTrader，无法进行随机购买")
                    return results

            # 如果没有配置trader，返回错误
            results["errors"].append("未配置ConcurrentTrader，无法执行高速购买")
            return results
            
        except Exception as e:
            logger.error(f"❌ 执行购买任务失败: {e}")
            results["errors"].append(f"执行购买任务失败: {e}")
            return results
    
    async def start_auto_trading(self, config: AutoBuyConfig) -> Dict[str, Any]:
        """启动自动交易
        
        Args:
            config: 自动交易配置
            
        Returns:
            交易结果统计
        """
        trading_results = {
            "config_name": config.name,
            "start_time": datetime.now().isoformat(),
            "targets_processed": 0,
            "total_successful_purchases": 0,
            "total_failed_purchases": 0,
            "target_results": [],
            "errors": []
        }
        
        try:
            logger.info(f"🚀 启动自动交易: {config.name}")
            logger.info(f"📋 配置描述: {config.description}")
            logger.info(f"🎯 目标数量: {len(config.targets)}")
            logger.info(f"🔍 干运行模式: {config.dry_run}")
            
            # 验证配置
            validation_errors = config.validate()
            if validation_errors:
                error_msg = f"配置验证失败: {', '.join(validation_errors)}"
                logger.error(f"❌ {error_msg}")
                trading_results["errors"].append(error_msg)
                return trading_results
            
            # 重置停止事件
            self._stop_event.clear()
            
            # 处理每个购买目标（循环模式）
            for target in config.targets:
                if self._stop_event.is_set():
                    logger.info("🛑 自动交易被中断")
                    break

                try:
                    logger.info(f"🎯 开始处理目标: {target.project_name}")

                    # 启动循环购买流程
                    await self._start_continuous_trading(target, config, trading_results)

                except Exception as e:
                    error_msg = f"处理目标 {target.project_name} 失败: {e}"
                    logger.error(f"❌ {error_msg}")
                    trading_results["errors"].append(error_msg)
            
            # 输出最终统计
            trading_results["end_time"] = datetime.now().isoformat()
            logger.info(f"🏁 自动交易完成: {config.name}")
            logger.info(f"📊 最终统计:")
            logger.info(f"   处理目标: {trading_results['targets_processed']}/{len(config.targets)}")
            logger.info(f"   总成功购买: {trading_results['total_successful_purchases']}")
            logger.info(f"   总失败购买: {trading_results['total_failed_purchases']}")
            
            return trading_results
            
        except Exception as e:
            error_msg = f"自动交易执行失败: {e}"
            logger.error(f"❌ {error_msg}")
            trading_results["errors"].append(error_msg)
            trading_results["end_time"] = datetime.now().isoformat()
            return trading_results

    async def _start_continuous_trading(self, target: AutoBuyTarget, config: AutoBuyConfig, trading_results: Dict[str, Any]):
        """启动持续交易流程

        Args:
            target: 购买目标
            config: 交易配置
            trading_results: 交易结果统计
        """
        logger.info(f"🔄 启动 {target.project_name} 的持续交易模式")

        # 阶段1：启动时检测当前可交易NFT，直接执行交易
        logger.info(f"🔍 阶段1：检查 {target.project_name} 当前可购买的NFT")
        immediate_result = await self._check_and_buy_immediately(target, config.dry_run)
        self._update_trading_results(trading_results, immediate_result)

        # 阶段2：进入轮次等待和购买循环
        round_count = 1

        self.forceLoadAvaiable(target)
        while not self._stop_event.is_set():
            try:
                logger.info(f"🔄 阶段2：开始第 {round_count} 轮等待和购买流程")

                leftTime = 99999999
                nextRoundTime = leftTime
                forceLoad = False
                while True:
                    # 等待下一轮开始时间
                    if leftTime > 5 * 60:
                        if config.schedule.use_api_time:
                            # 获取项目信息
                            project_info = await self.project_manager.get_project_with_cache(target.mint_pubkey)
                            if not project_info:
                                logger.error(f"❌ 无法获取项目 {target.project_name} 的信息")
                                await asyncio.sleep(10)
                                continue
                            nextRoundTime = project_info.last_round + project_info.sec_per_round
                            # 异常轮修复
                            while nextRoundTime + 60 - time.time() < 0:
                                nextRoundTime += project_info.sec_per_round

                    t = time.time()
                    leftTime =  nextRoundTime - t

                    # 5分钟以上 休息1分钟
                    if leftTime > 5 * 60:
                        logger.info(f"下一轮剩余时间:{leftTime}")
                        await asyncio.sleep(60)
                        continue

                    elif leftTime < 5 * 60:
                        # 获取一次nft列表
                        if leftTime > 4 * 60:
                            # 先更新本地NFT缓存
                            await self._refresh_nft_cache(target)
                            await asyncio.sleep(60)
                            if not forceLoad:
                                self.forceLoadAvaiable(target)
                                forceLoad = True
                            continue
                        else:
                            if leftTime > config.schedule.countdown_seconds + 10:
                                if not forceLoad:
                                    self.forceLoadAvaiable(target)
                                    forceLoad = True
                                logger.info(f"下一轮剩余时间:{leftTime}")
                                await asyncio.sleep(10)
                                continue
                            elif leftTime > 1:
                                logger.info(f"倒计时:{leftTime}")
                                await asyncio.sleep(1)
                                continue
                            else:
                                await asyncio.sleep(leftTime)

                    # 轮次开始：直接执行交易（不检测NFT可用性）
                    logger.info(f"🚀 第 {round_count} 轮开始：直接执行交易")
                    target_result = await self.execute_target_purchases(target, config.dry_run)
                    self._update_trading_results(trading_results, target_result)

                    logger.info(f"✅ 第 {round_count} 轮购买完成")
                    round_count += 1

                    # 短暂休息后继续下一轮
                    await asyncio.sleep(60 * 5)
                    break

            except Exception as e:
                error_msg = f"第 {round_count} 轮交易失败: {e}"
                logger.error(f"❌ {error_msg}")
                trading_results["errors"].append(error_msg)
                round_count += 1

                # 出错后等待一段时间再继续
                await asyncio.sleep(5)

    async def _check_and_buy_immediately(self, target: AutoBuyTarget, dry_run: bool) -> Dict[str, Any]:
        """检查并立即购买当前可用的NFT

        Args:
            target: 购买目标
            dry_run: 是否为干运行模式

        Returns:
            购买结果
        """
        try:
            # 检查当前是否有可购买的NFT
            can_buy_now = await self._check_can_buy_now(target)

            if can_buy_now:
                logger.info(f"✅ {target.project_name} 当前有可购买的NFT，立即执行购买")
                return await self.execute_target_purchases(target, dry_run)
            else:
                logger.info(f"⏳ {target.project_name} 当前无可购买的NFT，等待下一轮")
                return {
                    "target_name": target.project_name,
                    "successful_purchases": 0,
                    "failed_purchases": 0,
                    "errors": [],
                    "purchase_details": []
                }

        except Exception as e:
            logger.error(f"❌ 检查当前轮状态失败: {e}")
            return {
                "target_name": target.project_name,
                "successful_purchases": 0,
                "failed_purchases": 0,
                "errors": [f"检查当前轮状态失败: {e}"],
                "purchase_details": []
            }

    async def _check_can_buy_now(self, target: AutoBuyTarget) -> bool:
        """检查当前是否有可购买的NFT

        Args:
            target: 购买目标

        Returns:
            True表示有可购买的NFT，False表示需要等待
        """
        try:
            # 获取项目信息
            project_info = await self.project_manager.get_project_with_cache(target.mint_pubkey)
            if not project_info:
                logger.warning(f"⚠️ 无法获取项目信息: {target.mint_pubkey}")
                return False

            # PPP.fun是连续轮次机制，只要有NFT上架就可以购买
            # 不需要基于时间判断轮次，直接检查NFT可用性
            logger.info(f"🔍 检查是否有可购买的NFT（PPP.fun连续轮次机制）")

            # 检查是否有可购买的NFT
            if self.trader:
                # 初始化NFT池来检查可用性
                pool_initialized = await self.trader._initialize_nft_pool(target)
                if pool_initialized:
                    pool_size = len(self.trader._available_nft_pool)
                    logger.info(f"✅ 找到 {pool_size} 个可购买的NFT")
                    return True
                else:
                    logger.info(f"⏳ 无可购买的NFT")
                    return False
            else:
                # 如果没有trader，认为可以购买
                logger.info(f"✅ 未配置ConcurrentTrader，默认可购买")
                return True

        except Exception as e:
            logger.error(f"❌ 检查购买状态失败: {e}")
            return False

    def _update_trading_results(self, trading_results: Dict[str, Any], target_result: Dict[str, Any]):
        """更新交易结果统计

        Args:
            trading_results: 总体交易结果
            target_result: 单次目标交易结果
        """
        if target_result["successful_purchases"] > 0 or target_result["failed_purchases"] > 0:
            trading_results["targets_processed"] += 1
            trading_results["total_successful_purchases"] += target_result["successful_purchases"]
            trading_results["total_failed_purchases"] += target_result["failed_purchases"]
            trading_results["target_results"].append(target_result)

            # 如果有错误，记录到总错误列表
            if target_result["errors"]:
                trading_results["errors"].extend(target_result["errors"])

    def stop(self):
        """停止自动交易"""
        logger.info("🛑 请求停止自动交易")
        self._stop_event.set()
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return not self._stop_event.is_set()
