#!/usr/bin/env python3
"""
批量销毁用户NFT脚本
按照last_trade倒序执行销毁
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from ppp_simple_buyer import PPPSimpleBuyer
from nft_burn_manager import NFTBurnManager
from okx_token_seller import OKXTokenSeller
from simple_config import load_config
import time
import threading
from datetime import datetime

# 导入 Bark 推送模块
try:
    from bark_notifier import init_bark, safe_bark_notify_with_title
    BARK_AVAILABLE = True
except ImportError:
    BARK_AVAILABLE = False



class TokenMonitorState:
    """Token监控状态"""
    def __init__(self):
        self.running = False

async def token_monitor_loop(okx_seller, token_mint, threshold, state):
    """Token监控循环"""
    print(f"🔍 开始监控token余额: {token_mint}")

    # 发送监控开始通知
    if BARK_AVAILABLE:
        safe_bark_notify_with_title(
            "🪙 Token 监控启动",
            f"开始监控 Token 余额，阈值: {threshold}",
            "info"
        )

    last_balance = None
    threshold_exceeded_notified = False

    while state.running:
        try:
            # 获取token余额
            balance = await okx_seller.get_token_balance(token_mint)
            readable_balance = balance / 1_000_000  # 假设6位小数

            print(f"💰 Token余额: {readable_balance:.6f} ({balance})")

            # 检查余额变化（发送余额更新通知）
            if last_balance is not None and abs(readable_balance - last_balance) > 0.1:
                change = readable_balance - last_balance
                change_text = f"增加 {change:.6f}" if change > 0 else f"减少 {abs(change):.6f}"
                if BARK_AVAILABLE:
                    safe_bark_notify_with_title(
                        "💰 Token 余额变化",
                        f"余额 {change_text}，当前: {readable_balance:.6f}",
                        "info"
                    )

            # 检查是否超过阈值
            if readable_balance > threshold:
                print(f"🚨 余额 {readable_balance:.6f} 超过阈值 {threshold}，开始卖出")

                # 发送超过阈值警告（只发送一次）
                if not threshold_exceeded_notified:
                    if BARK_AVAILABLE:
                        safe_bark_notify_with_title(
                            "🚨 Token 余额超过阈值",
                            f"余额 {readable_balance:.6f} 超过阈值 {threshold}，开始自动卖出",
                            "warning"
                        )
                    threshold_exceeded_notified = True

                # 执行卖出
                result = await okx_seller.sell_all_tokens(token_mint, slippage_percent=50.0)

                if result["success"]:
                    print(f"✅ Token卖出成功: {result.get('transaction_signature')}")
                    # 发送卖出成功通知
                    if BARK_AVAILABLE:
                        safe_bark_notify_with_title(
                            "✅ Token 自动卖出成功",
                            f"成功卖出 Token，交易签名: {result.get('transaction_signature', 'N/A')[:8]}...",
                            "success"
                        )
                    # 重置通知状态
                    threshold_exceeded_notified = False
                else:
                    print(f"❌ Token卖出失败: {result.get('error_message')}")
                    # 发送卖出失败通知
                    if BARK_AVAILABLE:
                        safe_bark_notify_with_title(
                            "❌ Token 自动卖出失败",
                            f"卖出失败: {result.get('error_message', '未知错误')}",
                            "error"
                        )
            else:
                # 余额低于阈值，重置通知状态
                threshold_exceeded_notified = False

            last_balance = readable_balance
            # 等待3秒
            await asyncio.sleep(3)

        except Exception as e:
            print(f"❌ Token监控异常: {e}")
            # 发送监控异常通知
            if BARK_AVAILABLE:
                safe_bark_notify_with_title(
                    "❌ Token 监控异常",
                    f"监控出现异常: {str(e)[:50]}...",
                    "error"
                )
            await asyncio.sleep(3)

async def start_auto_monitor(burn_manager, user_nfts):
    """启动自动监控模式"""
    print("\n🔥 自动监控模式")
    print("=" * 50)

    # 显示NFT状态
    current_time = int(time.time())

    # 统计可销毁和不可销毁的NFT
    burnable_nfts = [nft for nft in user_nfts if nft['can_burn']]
    waiting_nfts = [nft for nft in user_nfts if not nft['can_burn']]

    print("📋 当前NFT状态:")
    for i, nft in enumerate(user_nfts[:10], 1):  # 只显示前10个
        status_icon = "🔥" if nft['can_burn'] else "⏳"
        print(f"   {i:2d}. {status_icon} NFT #{nft['nft_id']:3d} ({nft['token_symbol']:8s}) - {nft['time_info']}")

    if len(user_nfts) > 10:
        print(f"   ... 还有 {len(user_nfts) - 10} 个NFT")

    print(f"\n📊 统计: {len(burnable_nfts)} 个NFT可立即销毁，{len(waiting_nfts)} 个NFT需要等待")

    # 配置选项
    print("\n⚙️ 监控配置:")

    # 销毁延迟时间
    burn_delay_hours = 48

    check_interval = 1

    # Token监控配置
    target_token_mint = "5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp"
    token_threshold = 100  # 余额阈值

    # 运行模式
    print("\n🎯 运行模式:")
    print("1. 干运行监控（安全）")
    print("2. 真实销毁监控（危险）")

    dry_run = False

    # Token监控选项
    print("\n🪙 Token自动卖出:")
    print("1. 启用Token监控（推荐）")
    print("2. 仅NFT监控")

    enable_token_monitor = True

    # 最终确认
    print(f"\n📋 监控配置:")
    print(f"   销毁延迟: {burn_delay_hours} 小时")
    print(f"   检测间隔: {check_interval} 秒")
    print(f"   运行模式: {'干运行' if dry_run else '真实销毁'}")
    print(f"   监控NFT: {len(user_nfts)} 个")

    if enable_token_monitor:
        print(f"\n🪙 Token监控配置:")
        print(f"   目标Token: {target_token_mint}")
        print(f"   余额阈值: {token_threshold} (可读单位)")
        print(f"   滑点设置: 50%")
        print(f"   DEX: OKX聚合器")

    print("\n🚀 启动自动监控...")
    print("按 Ctrl+C 停止监控")
    print("=" * 50)

    # 启动token监控线程（如果启用）
    token_monitor_thread = None
    token_monitor_state = None

    if enable_token_monitor:
        try:
            # 创建OKX token卖出器
            okx_seller = OKXTokenSeller(burn_manager.buyer.wallet_keypair, burn_manager.buyer.rpc_url)

            # 创建监控状态
            token_monitor_state = TokenMonitorState()
            token_monitor_state.running = True

            # 启动token监控线程
            token_monitor_thread = threading.Thread(
                target=lambda: asyncio.run(token_monitor_loop(okx_seller, target_token_mint, token_threshold, token_monitor_state)),
                daemon=True
            )
            token_monitor_thread.start()
            print("🪙 Token监控线程已启动")

            # 发送监控启动成功通知
            if BARK_AVAILABLE:
                safe_bark_notify_with_title(
                    "🚀 自动监控已启动",
                    f"NFT 销毁监控和 Token 自动卖出已启动，阈值: {token_threshold}",
                    "success"
                )

        except Exception as e:
            print(f"⚠️ Token监控启动失败: {e}")
            # 发送监控启动失败通知
            if BARK_AVAILABLE:
                safe_bark_notify_with_title(
                    "❌ Token 监控启动失败",
                    f"Token 监控启动失败: {str(e)[:50]}...",
                    "error"
                )
    else:
        # 仅启动 NFT 监控时的通知
        if BARK_AVAILABLE:
            safe_bark_notify_with_title(
                "🚀 NFT 监控已启动",
                "NFT 销毁监控已启动（未启用 Token 自动卖出）",
                "success"
            )

    try:
        await burn_manager.auto_burn_monitor(
            burn_delay_hours=burn_delay_hours,
            check_interval_seconds=check_interval,
            dry_run=dry_run
        )
    except KeyboardInterrupt:
        print("\n⏹️ 监控已停止")
        # 发送监控停止通知
        if BARK_AVAILABLE:
            safe_bark_notify_with_title(
                "⏹️ 自动监控已停止",
                "NFT 销毁监控和 Token 自动卖出已手动停止",
                "warning"
            )
    finally:
        # 停止token监控
        if enable_token_monitor and token_monitor_state:
            token_monitor_state.running = False
            print("🛑 Token监控线程已停止")
            # 发送 Token 监控停止通知
            if BARK_AVAILABLE:
                safe_bark_notify_with_title(
                    "🛑 Token 监控已停止",
                    "Token 自动卖出监控已停止",
                    "info"
                )

async def main():
    """主函数"""
    print("🔥 批量NFT销毁工具")
    print("=" * 50)
    
    try:
        # 加载配置
        config = load_config()
        
        async with PPPSimpleBuyer(config.private_key) as buyer:
            print("✅ 买入器初始化成功")
            print(f"📍 钱包地址: {buyer.wallet_keypair.pubkey()}")

            # 显示余额
            await buyer._display_wallet_balance()

            # 创建NFT管理器
            burn_manager = NFTBurnManager(buyer)

            print("\n🔍 获取用户NFT列表...")

            # 获取用户的所有NFT
            user_nfts = await burn_manager.get_user_nfts_with_time_info()
            
            if not user_nfts:
                print("❌ 未找到任何NFT")
                return
            
            # 统计可销毁和不可销毁的NFT
            burnable_nfts = [nft for nft in user_nfts if nft['can_burn']]
            waiting_nfts = [nft for nft in user_nfts if not nft['can_burn']]

            print(f"✅ 找到 {len(user_nfts)} 个NFT")
            print(f"   🔥 可立即销毁: {len(burnable_nfts)} 个")
            print(f"   ⏳ 需要等待: {len(waiting_nfts)} 个")

            print("\n📋 NFT列表（按last_trade升序，最早交易的优先）:")

            # 显示NFT列表（显示剩余时间）
            for i, nft in enumerate(user_nfts, 1):
                status_icon = "🔥" if nft['can_burn'] else "⏳"
                print(f"   {i:2d}. {status_icon} NFT #{nft['nft_id']:3d} ({nft['token_symbol']:8s}) - {nft['project_name']}")
                print(f"       {nft['time_info']}")
                print(f"       project_mint: {nft['project_mint']}")
                print()
            
            # 用户选择
            print("🎯 操作选项:")
            print("1. 批量销毁干运行（立即销毁满足条件的NFT）")
            print("2. 批量销毁真实执行（危险）")
            print("3. 自动监控模式（持续监控，时间到了自动销毁）")
            print("4. 退出")

            choice = input("\n请选择操作 (1/2/3/4): ").strip()
            
            if choice == "4":
                print("👋 已退出")
                return
            elif choice == "3":
                # 自动监控模式
                await start_auto_monitor(burn_manager, user_nfts)
                return
            elif choice not in ["1", "2"]:
                print("❌ 无效选择")
                return
            
            dry_run = choice == "1"
            mode_text = "批量销毁干运行" if dry_run else "批量销毁真实执行"
            
            # 选择销毁数量
            print(f"\n🔢 选择{mode_text}数量:")
            print(f"1. 全部 ({len(user_nfts)} 个)")
            print("2. 指定数量")
            print("3. 取消")
            
            count_choice = input("请选择 (1/2/3): ").strip()
            
            if count_choice == "3":
                print("👋 已取消")
                return
            elif count_choice == "1":
                max_count = None
                count_text = f"全部 {len(user_nfts)} 个"
            elif count_choice == "2":
                try:
                    max_count = int(input(f"请输入数量 (1-{len(user_nfts)}): "))
                    if max_count <= 0 or max_count > len(user_nfts):
                        print("❌ 无效数量")
                        return
                    count_text = f"{max_count} 个"
                except ValueError:
                    print("❌ 无效输入")
                    return
            else:
                print("❌ 无效选择")
                return
            
            # 最终确认
            print(f"\n⚠️  确认{mode_text}:")
            print(f"   模式: {mode_text}")
            print(f"   数量: {count_text}")
            print(f"   钱包: {buyer.wallet_keypair.pubkey()}")
            
            print(f"\n🚀 开始{mode_text}...")
            print("=" * 50)
            
            # 执行批量销毁
            result = await burn_manager.batch_burn_nfts(
                dry_run=dry_run,
                max_count=max_count
            )
            
            print("\n" + "=" * 50)
            print("📊 销毁结果:")
            
            if result["success"]:
                print(f"✅ {result['message']}")
                print(f"   总NFT数: {result.get('total_nfts', 0)}")
                print(f"   处理数: {result.get('target_nfts', 0)}")
                print(f"   成功: {result['success_count']}")
                print(f"   失败: {result['failed_count']}")
                
                # 显示详细结果
                if result['results']:
                    print("\n📋 详细结果:")
                    for i, item in enumerate(result['results'], 1):
                        status = "✅" if item['result'].get('success') else "❌"
                        print(f"   {i:2d}. {status} NFT #{item['nft_id']} ({item['token_symbol']})")
                        if not item['result'].get('success'):
                            print(f"       错误: {item['result'].get('error_message', '未知错误')}")
                
                if not dry_run and result['success_count'] > 0:
                    print(f"\n🔗 查看交易:")
                    for item in result['results']:
                        if item['result'].get('success') and item['result'].get('transaction_signature') != 'dry_run_simulation':
                            sig = item['result']['transaction_signature']
                            print(f"   NFT #{item['nft_id']}: https://solscan.io/tx/{sig}")
            else:
                print(f"❌ {result['message']}")
            
    except Exception as e:
        print(f"❌ 批量销毁失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
