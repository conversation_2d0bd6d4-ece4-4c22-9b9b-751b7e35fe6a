"""
PPP.fun API 客户端
用于获取项目信息和 NFT 数据
"""
import asyncio
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from decimal import Decimal

import aiohttp

# 日志配置
from log_config import setup_api_logging, get_logger

# 初始化日志配置（使用全局项目名称）
setup_api_logging()
logger = get_logger()


@dataclass
class PPPProject:
    """PPP 项目信息"""
    id: str
    name: str
    symbol: str
    description: str
    image: str
    volume: Decimal
    floor_price: Decimal
    total_supply: int
    listed_count: int
    creator: str
    created_at: str
    project_mint: str = ""  # 项目mint地址，用于查询NFT
    
    def __str__(self):
        return f"{self.name} ({self.symbol}) - 地板价: {self.floor_price} SOL, 成交量: {self.volume} SOL"


@dataclass
class PPPNFTItem:
    """PPP NFT 项目"""
    id: int
    name: str
    image: str
    price: Decimal
    mint_address: str
    owner: str
    listed: bool
    rarity_rank: Optional[int]
    attributes: Dict[str, Any]
    round: int = 0
    
    def __str__(self):
        status = "可购买" if self.listed else "未上架"
        return f"#{self.id} {self.name} - {self.price} SOL ({status})"


class PPPAPIClient:
    """PPP.fun API 客户端"""
    
    BASE_URL = "https://api.ppp.fun"
    
    def __init__(self, timeout: float = 30.0):
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送 HTTP 请求"""
        if not self.session:
            raise RuntimeError("请在 async with 语句中使用 PPPAPIClient")
        
        url = f"{self.BASE_URL}{endpoint}"
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    error_text = await response.text()
                    logger.error(f"API 请求失败: {response.status} - {error_text}")
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status,
                        message=error_text
                    )
        except aiohttp.ClientError as e:
            logger.error(f"网络请求失败: {e}")
            raise
    
    async def get_projects(self, limit: int = 20, page: int = 1, keyword: str = "", sort: str = "volume") -> List[PPPProject]:
        """获取项目列表"""
        try:
            logger.info(f"获取项目列表: limit={limit}, page={page}, keyword='{keyword}', sort={sort}")
            
            params = {
                "limit": limit,
                "page": page,
                "keyword": keyword,
                "sort": sort
            }
            
            data = await self._request("GET", "/projects", params=params)
            
            projects = []
            for item in data.get("data", []):
                try:
                    # 计算地板价 (从 init_nft_price 转换，单位是 lamports)
                    floor_price_lamports = item.get("init_nft_price", 0) or 0
                    floor_price_sol = Decimal(floor_price_lamports) / Decimal(10**9)

                    # 计算24小时成交量 (从 volume_24h 转换，单位是 lamports)
                    volume_24h_lamports = item.get("volume_24h", 0) or 0
                    volume_24h_sol = Decimal(volume_24h_lamports) / Decimal(10**9)

                    project = PPPProject(
                        id=item.get("project_pubkey", ""),
                        name=item.get("project_name", ""),
                        symbol=item.get("token_symbol", ""),
                        description=item.get("project_desc", ""),
                        image=item.get("image_url", ""),
                        volume=volume_24h_sol,
                        floor_price=floor_price_sol,
                        total_supply=int(item.get("max_nft_supply", 0)),
                        listed_count=int(item.get("nft_issue_count", 0)),
                        creator=item.get("creator_pubkey", ""),
                        created_at=item.get("create_time", ""),
                        project_mint=item.get("mint_pubkey", "")
                    )
                    projects.append(project)
                except (ValueError, TypeError) as e:
                    logger.warning(f"解析项目数据失败: {e}, 数据: {item}")
                    continue
            
            logger.info(f"成功获取 {len(projects)} 个项目")
            return projects
            
        except Exception as e:
            logger.error(f"获取项目列表失败: {e}")
            return []
    
    async def get_project_nfts(self, project_mint: str, limit: int = 100, page: int = 1,
                              sort: str = "price_asc") -> List[PPPNFTItem]:
        """获取项目的 NFT 列表"""
        try:
            logger.info(f"获取项目 {project_mint} 的 NFT 列表")

            all_nfts = []
            current_page = 1

            # 获取全部 NFT 数据
            while True:
                params = {
                    "limit": limit,
                    "page": current_page
                }

                # 使用 mint_pubkey 作为项目标识符
                endpoint = f"/project/{project_mint}/nfts"
                data = await self._request("GET", endpoint, params=params)

                page_nfts = []
                for item in data.get("data", []):
                    try:
                        # 价格从 lamports 转换为 SOL
                        price_lamports = item.get("price", 0)
                        price_sol = Decimal(price_lamports) / Decimal(10**9)

                        # 判断是否可购买 (未销毁且有拥有者)
                        owner_pubkey = item.get("owner_pubkey")

                        nft = PPPNFTItem(
                            id=int(item.get("nft_id", 0)),
                            name=f"NFT #{item.get('nft_id', 0)}",  # PPP.fun 没有单独的名称字段
                            image="",  # PPP.fun API 没有返回图片URL
                            price=price_sol,
                            mint_address=item.get("nft_pubkey", ""),  # 使用 nft_pubkey 作为 mint 地址
                            owner=item.get("owner_pubkey", ""),
                            listed=owner_pubkey,
                            rarity_rank=None,  # PPP.fun 没有稀有度排名
                            attributes={
                                "round": item.get("round", 0),
                                "split_count": item.get("split_count", 0),
                                "last_trade": item.get("last_trade", 0),
                                "last_split": item.get("last_split", 0),
                                "create_time": item.get("create_time", ""),
                                "bump": item.get("bump", 0),
                            }
                        )
                        page_nfts.append(nft)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"解析 NFT 数据失败: {e}, 数据: {item}")
                        continue

                all_nfts.extend(page_nfts)

                # 检查是否还有更多页面
                meta = data.get("meta", {})
                if not meta.get("has_more", False) or len(page_nfts) == 0:
                    break

                current_page += 1
                logger.debug(f"获取第 {current_page} 页...")

            logger.info(f"成功获取全部 {len(all_nfts)} 个 NFT")
            return all_nfts

        except Exception as e:
            logger.error(f"获取 NFT 列表失败: {e}")
            return []
    
    async def get_project_info(self, project_id: str) -> Optional[PPPProject]:
        """获取单个项目详细信息"""
        try:
            logger.info(f"获取项目 {project_id} 详细信息")
            
            endpoint = f"/projects/{project_id}"
            data = await self._request("GET", endpoint)
            
            project_data = data.get("data", {})
            if not project_data:
                logger.warning(f"项目 {project_id} 不存在")
                return None
            
            project = PPPProject(
                id=project_data.get("id", ""),
                name=project_data.get("name", ""),
                symbol=project_data.get("symbol", ""),
                description=project_data.get("description", ""),
                image=project_data.get("image", ""),
                volume=Decimal(str(project_data.get("volume", 0))),
                floor_price=Decimal(str(project_data.get("floor_price", 0))),
                total_supply=int(project_data.get("total_supply", 0)),
                listed_count=int(project_data.get("listed_count", 0)),
                creator=project_data.get("creator", ""),
                created_at=project_data.get("created_at", "")
            )
            
            logger.info(f"成功获取项目信息: {project.name}")
            return project
            
        except Exception as e:
            logger.error(f"获取项目信息失败: {e}")
            return None
    
    async def search_projects(self, keyword: str, limit: int = 10) -> List[PPPProject]:
        """搜索项目"""
        return await self.get_projects(limit=limit, keyword=keyword, sort="volume")

    async def get_user_nfts(self, wallet_address: str) -> List[Dict[str, Any]]:
        """获取用户持有的NFT，按last_trade倒序排列"""
        try:
            logger.info(f"获取用户 {wallet_address} 的NFT")

            endpoint = f"/user/{wallet_address}/nfts"
            data = await self._request("GET", endpoint)

            user_nfts = []
            projects_data = data.get("data", {}).get("projects", [])

            for project in projects_data:
                project_mint = project.get("mint_pubkey", "")
                project_name = project.get("project_name", "")
                token_symbol = project.get("token_symbol", "")

                nfts = project.get("nfts", [])
                for nft in nfts:
                    nft_info = {
                        "nft_id": nft.get("nft_id"),
                        "nft_pubkey": nft.get("nft_pubkey"),
                        "project_mint": project_mint,
                        "project_name": project_name,
                        "token_symbol": token_symbol,
                        "price": nft.get("price", 0),
                        "round": nft.get("round", 0),
                        "last_trade": nft.get("last_trade", 0),
                        "create_time": nft.get("create_time", ""),
                        "owner_pubkey": wallet_address  # 用户持有的NFT，owner就是用户
                    }
                    user_nfts.append(nft_info)

            # 按last_trade升序排列（最早交易的优先）
            user_nfts.sort(key=lambda x: x.get("last_trade", 0), reverse=False)

            logger.info(f"成功获取用户 {len(user_nfts)} 个NFT，已按last_trade升序排列")
            return user_nfts

        except Exception as e:
            logger.error(f"获取用户NFT失败: {e}")
            return []


# 便捷函数
async def get_popular_projects(limit: int = 20) -> List[PPPProject]:
    """获取热门项目"""
    async with PPPAPIClient() as client:
        return await client.get_projects(limit=limit, sort="volume")


async def search_projects_by_name(keyword: str, limit: int = 10) -> List[PPPProject]:
    """按名称搜索项目"""
    async with PPPAPIClient() as client:
        return await client.search_projects(keyword, limit)


async def get_project_available_nfts(project_mint: str, max_price: Optional[Decimal] = None) -> List[PPPNFTItem]:
    """获取项目可购买的 NFT"""
    async with PPPAPIClient() as client:
        nfts = await client.get_project_nfts(project_mint, sort="price_asc")

        if max_price:
            nfts = [nft for nft in nfts if nft.price <= max_price]

        return nfts


# 测试函数
async def test_api():
    """测试 API 功能"""
    print("🧪 测试 PPP.fun API...")
    
    try:
        # 测试获取项目列表
        projects = await get_popular_projects(5)
        print(f"✅ 获取到 {len(projects)} 个热门项目:")
        for i, project in enumerate(projects, 1):
            print(f"   {i}. {project}")
        
        if projects:
            # 测试获取 NFT 列表 - 使用第三个项目 (CAI) 因为它有 NFT 数据
            test_project = None
            for project in projects:
                if "CAI" in project.symbol or "Consensus" in project.name:
                    test_project = project
                    break

            if not test_project:
                test_project = projects[0]  # 如果没找到 CAI，使用第一个

            print(f"\n🔍 获取项目 '{test_project.name}' 的 NFT...")
            # 从项目数据中获取 mint_pubkey
            mint_pubkey = "63B7LWnQDwAPybnjpiLrEEx1cVGG9jwxmQBc3BAMWppp"  # CAI 项目的 mint
            nfts = await get_project_available_nfts(mint_pubkey)
            print(f"✅ 获取到 {len(nfts)} 个可购买的 NFT")

            for i, nft in enumerate(nfts[:3], 1):  # 只显示前3个
                print(f"   {i}. {nft}")
        
    except Exception as e:
        print(f"❌ API 测试失败: {e}")


@dataclass
class ProjectInfo:
    """项目信息数据类，用于缓存"""
    mint: str
    name: str
    symbol: str
    sec_per_round: int
    last_round: int
    total_supply: int
    last_updated: datetime

    def calculate_next_round_time(self) -> datetime:
        """根据last_round和sec_per_round计算下一轮时间"""
        current_time = datetime.now().timestamp()
        next_round_timestamp = self.last_round

        # 循环计算，直到找到未来的轮次时间
        while next_round_timestamp <= current_time:
            next_round_timestamp += self.sec_per_round

        return datetime.fromtimestamp(next_round_timestamp)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式用于JSON序列化"""
        return {
            "mint": self.mint,
            "name": self.name,
            "symbol": self.symbol,
            "sec_per_round": self.sec_per_round,
            "last_round": self.last_round,
            "total_supply": self.total_supply,
            "last_updated": self.last_updated.isoformat()
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectInfo':
        """从字典创建ProjectInfo实例"""
        return cls(
            mint=data["mint"],
            name=data["name"],
            symbol=data["symbol"],
            sec_per_round=data["sec_per_round"],
            last_round=data["last_round"],
            total_supply=data["total_supply"],
            last_updated=datetime.fromisoformat(data["last_updated"])
        )


class ProjectDataManager:
    """项目数据管理器，扩展PPPAPIClient功能"""

    def __init__(self, api_client: PPPAPIClient):
        """初始化项目数据管理器

        Args:
            api_client: PPPAPIClient实例，复用现有API功能
        """
        self.api_client = api_client
        self.data_dir = Path("project_data")
        self.data_dir.mkdir(exist_ok=True)
        logger.info(f"📁 项目数据目录: {self.data_dir}")

    def _get_cache_file_path(self, project_mint: str) -> Path:
        """获取项目缓存文件路径"""
        return self.data_dir / f"{project_mint}.json"

    async def get_project_with_cache(self, project_mint: str, force_refresh: bool = False) -> Optional[ProjectInfo]:
        """获取项目信息，支持缓存

        Args:
            project_mint: 项目mint地址
            force_refresh: 是否强制刷新缓存

        Returns:
            ProjectInfo实例或None
        """
        cache_file = self._get_cache_file_path(project_mint)

        # 尝试从缓存加载
        if not force_refresh and cache_file.exists():
            try:
                cached_info = self._load_from_cache(project_mint)
                if cached_info:
                    # 检查缓存是否过期（60分钟）
                    cache_age = datetime.now() - cached_info.last_updated
                    if cache_age.total_seconds() < 3600:
                        logger.info(f"📋 使用缓存的项目信息: {cached_info.name}")
                        return cached_info
                    else:
                        logger.info(f"⏰ 缓存已过期，重新获取项目信息: {project_mint}")
            except Exception as e:
                logger.warning(f"⚠️ 读取缓存失败: {e}")

        # 从API获取项目信息
        try:
            logger.info(f"🔍 从API获取项目信息: {project_mint}")

            # 直接调用原始API获取包含轮次信息的数据
            endpoint = "/projects"
            params = {"limit": 100, "page": 1}

            raw_data = await self.api_client._request("GET", endpoint, params=params)
            projects_data = raw_data.get("data", [])

            # 查找匹配的项目
            target_project_data = None
            for item in projects_data:
                project_mint_from_api = item.get("mint_pubkey")
                if project_mint_from_api == project_mint:
                    target_project_data = item
                    break

            if not target_project_data:
                logger.warning(f"⚠️ 未找到项目: {project_mint}")
                return None

            # 直接从原始数据创建ProjectInfo
            project_info = ProjectInfo(
                mint=project_mint,
                name=target_project_data.get("project_name", "Unknown"),
                symbol=target_project_data.get("token_symbol", "Unknown"),
                sec_per_round=target_project_data.get("sec_per_round", 3600),
                last_round=target_project_data.get("last_round", int(datetime.now().timestamp())),
                total_supply=target_project_data.get("max_nft_supply", 0),
                last_updated=datetime.now()
            )

            # 保存到缓存
            await self._save_to_cache(project_info)
            logger.info(f"✅ 项目信息获取成功: {project_info.name}")
            return project_info

        except Exception as e:
            logger.error(f"❌ 获取项目信息失败: {e}")

            # 如果API失败，尝试使用过期的缓存
            if cache_file.exists():
                logger.info(f"🔄 API失败，使用过期缓存: {project_mint}")
                return self._load_from_cache(project_mint)

        return None

    async def _extract_project_info(self, project: PPPProject, project_mint: str) -> Optional[ProjectInfo]:
        """从PPPProject提取ProjectInfo"""
        try:
            # 直接从现有的project对象获取信息
            # 由于PPPProject可能不包含轮次信息，我们需要直接调用API获取原始数据

            # 调用原始API获取包含轮次信息的数据
            endpoint = "/projects"
            params = {"limit": 100}

            try:
                raw_data = await self.api_client._request("GET", endpoint, params=params)
                projects_data = raw_data.get("data", [])

                # 查找匹配的项目
                for item in projects_data:
                    project_mint_from_api = item.get("mint_pubkey") or item.get("project_mint")
                    if project_mint_from_api == project_mint:
                        # 找到匹配的项目，提取轮次信息
                        sec_per_round = item.get("sec_per_round", 3600)
                        last_round = item.get("last_round", int(datetime.now().timestamp()))

                        return ProjectInfo(
                            mint=project_mint,
                            name=item.get("project_name", project.name),
                            symbol=item.get("token_symbol", project.symbol),
                            sec_per_round=sec_per_round,
                            last_round=last_round,
                            total_supply=item.get("max_nft_supply", project.total_supply),
                            last_updated=datetime.now()
                        )

            except Exception as api_error:
                logger.warning(f"⚠️ 无法从API获取轮次信息: {api_error}")

            # 如果无法从API获取轮次信息，使用默认值
            logger.warning(f"⚠️ 无法获取轮次信息，使用默认值: {project.name}")
            return ProjectInfo(
                mint=project_mint,
                name=project.name,
                symbol=project.symbol,
                sec_per_round=3600,  # 默认1小时
                last_round=int(datetime.now().timestamp()),
                total_supply=project.total_supply,
                last_updated=datetime.now()
            )

        except Exception as e:
            logger.error(f"❌ 提取项目信息失败: {e}")
            return None

    def _load_from_cache(self, project_mint: str) -> Optional[ProjectInfo]:
        """从缓存文件加载项目信息"""
        cache_file = self._get_cache_file_path(project_mint)

        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return ProjectInfo.from_dict(data)
        except Exception as e:
            logger.error(f"❌ 加载缓存失败: {e}")
            return None

    async def _save_to_cache(self, project_info: ProjectInfo):
        """保存项目信息到缓存"""
        cache_file = self._get_cache_file_path(project_info.mint)

        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(project_info.to_dict(), f, indent=2, ensure_ascii=False)
            logger.debug(f"💾 项目信息已缓存: {cache_file}")
        except Exception as e:
            logger.error(f"❌ 保存缓存失败: {e}")

    def calculate_next_round_time(self, last_round: int, sec_per_round: int) -> datetime:
        """计算下一轮时间

        Args:
            last_round: 上一轮的时间戳
            sec_per_round: 每轮间隔秒数

        Returns:
            下一轮的时间
        """
        next_round_timestamp = last_round + sec_per_round
        return datetime.fromtimestamp(next_round_timestamp)

    async def check_current_round_tradeable(self, project_mint: str) -> bool:
        """检查当前轮是否可以交易

        通过NFT的last_trade字段判断是否在当前轮中有交易
        如果有交易记录且时间在当前轮范围内，说明当前轮可以交易

        Args:
            project_mint: 项目mint地址

        Returns:
            True表示当前轮可以交易，False表示需要等待下一轮
        """
        try:
            logger.info(f"🔍 检查项目 {project_mint} 当前轮交易状态")

            # 获取项目信息
            project_info = await self.get_project_with_cache(project_mint)
            if not project_info:
                logger.warning(f"⚠️ 无法获取项目信息: {project_mint}")
                return False

            # 获取项目的NFT列表
            nfts = await self.api_client.get_project_nfts(project_mint, limit=50)
            if not nfts:
                logger.warning(f"⚠️ 无法获取NFT列表: {project_mint}")
                return False

            current_time = datetime.now().timestamp()
            current_round_start = project_info.last_round

            # 检查是否有NFT在当前轮有交易记录
            for nft in nfts:
                # 检查NFT的属性中是否有last_trade字段
                last_trade = nft.attributes.get("last_trade", 0)
                if last_trade and last_trade > current_round_start:
                    logger.info(f"✅ 发现当前轮交易记录，NFT #{nft.id}, 交易时间: {datetime.fromtimestamp(last_trade)}")
                    return True

            logger.info(f"⏳ 当前轮暂无交易记录，需要等待下一轮")
            return False

        except Exception as e:
            logger.error(f"❌ 检查交易状态失败: {e}")
            return False

    async def update_project_round_info(self, project_mint: str, last_round: int, sec_per_round: int):
        """更新项目轮次信息

        Args:
            project_mint: 项目mint地址
            last_round: 最新的轮次时间戳
            sec_per_round: 每轮间隔秒数
        """
        try:
            # 获取现有项目信息
            project_info = await self.get_project_with_cache(project_mint)
            if not project_info:
                logger.warning(f"⚠️ 项目不存在，无法更新轮次信息: {project_mint}")
                return

            # 更新轮次信息
            project_info.last_round = last_round
            project_info.sec_per_round = sec_per_round
            project_info.last_updated = datetime.now()

            # 保存到缓存
            await self._save_to_cache(project_info)
            logger.info(f"✅ 项目轮次信息已更新: {project_info.name}")

        except Exception as e:
            logger.error(f"❌ 更新轮次信息失败: {e}")

    async def get_time_to_next_round(self, project_mint: str) -> Optional[float]:
        """获取距离下一轮的时间（秒）

        Args:
            project_mint: 项目mint地址

        Returns:
            距离下一轮的秒数，如果已经可以交易则返回0，失败返回None
        """
        try:
            project_info = await self.get_project_with_cache(project_mint)
            if not project_info:
                return None

            # 检查当前轮是否可以交易
            if await self.check_current_round_tradeable(project_mint):
                return 0.0

            # 计算下一轮时间
            next_round_time = project_info.calculate_next_round_time()
            current_time = datetime.now()

            time_diff = (next_round_time - current_time).total_seconds()
            return max(0.0, time_diff)  # 确保不返回负数

        except Exception as e:
            logger.error(f"❌ 计算下一轮时间失败: {e}")
            return None

    def clear_cache(self, project_mint: Optional[str] = None):
        """清理缓存

        Args:
            project_mint: 指定项目mint，如果为None则清理所有缓存
        """
        try:
            if project_mint:
                cache_file = self._get_cache_file_path(project_mint)
                if cache_file.exists():
                    cache_file.unlink()
                    logger.info(f"🗑️ 已清理项目缓存: {project_mint}")
            else:
                # 清理所有缓存文件
                for cache_file in self.data_dir.glob("*.json"):
                    cache_file.unlink()
                logger.info(f"🗑️ 已清理所有项目缓存")

        except Exception as e:
            logger.error(f"❌ 清理缓存失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_api())
